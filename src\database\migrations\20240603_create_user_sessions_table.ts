import { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  await knex.schema.createTable('user_sessions', (table) => {
    table.uuid('id').primary().defaultTo(knex.raw('gen_random_uuid()'));
    table.uuid('user_id').notNullable().references('id').inTable('users');
    table.string('session_token', 255).notNullable().unique();
    table.string('refresh_token', 255).unique().nullable();
    table.timestamp('expires_at').notNullable();
    table.timestamp('refresh_expires_at').nullable();
    table.boolean('is_active').notNullable().defaultTo(true);
    table.timestamp('created_at').notNullable().defaultTo(knex.fn.now());
    table.timestamp('last_accessed_at').notNullable().defaultTo(knex.fn.now());
    table.timestamp('logged_out_at').nullable();
    table.string('ip_address', 15).nullable();
    table.text('device_details').nullable();
  });
  await knex.raw(`CREATE INDEX idx_sessions_active ON user_sessions(user_id, session_token) WHERE is_active = true`);
  await knex.raw(`CREATE INDEX idx_sessions_refresh_token ON user_sessions(refresh_token)`);
}

export async function down(knex: Knex): Promise<void> {
  await knex.schema.dropTableIfExists('user_sessions');
} 