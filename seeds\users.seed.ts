import { <PERSON><PERSON> } from 'knex';
import { v4 as uuidv4 } from 'uuid';
import * as bcrypt from 'bcryptjs';

// Idempotent seed: deletes all and inserts sample users
export async function seed(knex: Knex): Promise<void> {
  // Deletes ALL existing entries
  await knex('users').del();

  // Hash passwords for seed users
  const hashedPassword1 = await bcrypt.hash('password123', 10);
  const hashedPassword2 = await bcrypt.hash('password456', 10);
  const hashedAdminPassword = await bcrypt.hash('admin123', 10);

  // Inserts seed entries
  await knex('users').insert([
    {
      id: uuidv4(),
      email: '<EMAIL>',
      password: hashedPassword1,
      name: 'Test User1',
      mobile_number: '**********',
      role: 'USER',
      is_active: true,
      email_verified: true,
      account_locked: false,
      failed_login_attempts: 0,
      last_login_at: null,
      social_login_provider: null,
      social_login_provider_id: null,
      social_login_provider_image_url: null,
      created_by: null,
      updated_by: null,
      created_on: new Date(),
      updated_on: new Date(),
    },
    {
      id: uuidv4(),
      email: '<EMAIL>',
      password: hashedPassword2,
      name: 'Test User2',
      mobile_number: '**********',
      role: 'USER',
      is_active: true,
      email_verified: false,
      account_locked: false,
      failed_login_attempts: 0,
      last_login_at: null,
      social_login_provider: null,
      social_login_provider_id: null,
      social_login_provider_image_url: null,
      created_by: null,
      updated_by: null,
      created_on: new Date(),
      updated_on: new Date(),
    },
    {
      id: uuidv4(),
      email: '<EMAIL>',
      password: hashedAdminPassword,
      name: 'Admin User',
      mobile_number: '**********',
      role: 'ADMIN',
      is_active: true,
      email_verified: true,
      account_locked: false,
      failed_login_attempts: 0,
      last_login_at: null,
      social_login_provider: null,
      social_login_provider_id: null,
      social_login_provider_image_url: null,
      created_by: null,
      updated_by: null,
      created_on: new Date(),
      updated_on: new Date(),
    },
  ]);
}