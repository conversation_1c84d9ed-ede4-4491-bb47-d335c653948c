name: Semgrep Static Analysis

on:
  workflow_call:

jobs:
  semgrep-analysis:
    name: Semgrep Static Analysis
    runs-on: [self-hosted, Linux]
    steps:
      - name: Checkout Code
        uses: actions/checkout@v3

      - name: Install doctl
        uses: digitalocean/action-doctl@v2
        with:
          token: ${{ secrets.DIGITALOCEAN_ACCESS_TOKEN }}

      - name: Install kubectl
        uses: azure/setup-kubectl@v3
        with:
          version: 'latest'

      - name: <PERSON><PERSON> Kubeconfig from DOKS
        run: doctl kubernetes cluster kubeconfig save ${{ secrets.KUBERNETES_CLUSTER_ID }}

      - name: Set Kubernetes Context
        run: kubectl config use-context ${{ secrets.KUBERNETES_CONTEXT }}

      - name: Ensure Namespace Exists
        run: kubectl create namespace semgrep || true

      - name: Create GitHub Token Secret
        run: |
          kubectl create secret generic secure-github-token \
            --from-literal=token=${{ secrets.SECURE_GITHUB_TOKEN }} \
            -n semgrep --dry-run=client -o yaml | kubectl apply -f -

      - name: Apply Semgrep Results PVC
        run: kubectl apply -f k8s/semgrep-pvc.yaml

      - name: Delete Previous Semgrep Job (If Exists)
        run: |
          kubectl delete job semgrep-job -n semgrep --ignore-not-found
          for i in {1..10}; do
            kubectl get job semgrep-job -n semgrep && sleep 2 || break
          done

      - name: Apply Semgrep Job with dynamic branch
        run: |
          BRANCH="${{ github.head_ref || github.ref_name }}"
           sed "s|value: \"main\" # Default, will be overridden by workflow|value: \"$BRANCH\"|" k8s/semgrep-job.yaml | kubectl apply -f -

      - name: Wait for Semgrep Job to Complete
        run: |
          kubectl wait --for=condition=complete job/semgrep-job -n semgrep --timeout=600s

      - name: Create Semgrep Results Reader Pod
        run: |
          kubectl delete pod semgrep-results-reader -n semgrep --ignore-not-found
          for i in {1..10}; do
            kubectl get pod semgrep-results-reader -n semgrep && sleep 2 || break
          done
          kubectl apply -f k8s/semgrep-results-reader.yaml

      - name: Wait for Reader Pod Ready
        run: |
          kubectl wait --for=condition=Ready pod/semgrep-results-reader -n semgrep --timeout=120s

      - name: List files in /results in reader pod
        run: |
          kubectl exec -n semgrep semgrep-results-reader -- ls -l /results

      - name: Copy Semgrep Results from PVC
        run: |
          kubectl cp semgrep/semgrep-results-reader:/results/results.json results.json

      - name: Upload Semgrep Results as Artifact
        uses: actions/upload-artifact@v4
        with:
          name: semgrep-results
          path: results.json
          if-no-files-found: warn
          compression-level: 6
          overwrite: false
          include-hidden-files: false

      - name: Install jq
        run: sudo apt-get update && sudo apt-get install -y jq

      - name: Fail if Semgrep found blocking issues
        run: |
          if jq '.results[] | select(.severity == "ERROR")' results.json | grep -q .; then
            echo "Semgrep found ERROR-level issues! Failing the workflow."
            echo "--- Semgrep Findings (first 100 lines) ---"
            jq '.results[] | select(.severity == "ERROR")' results.json | head -100
            echo "------------------------"
            exit 1
          else
            echo "No ERROR-level Semgrep issues found."
          fi
