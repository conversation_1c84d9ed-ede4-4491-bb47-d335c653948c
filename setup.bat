@echo off
setlocal enabledelayedexpansion

REM AI Nest Backend - Environment Setup Script (Windows)
REM This script helps set up environment files for development

echo.
echo 🚀 AI Nest Backend - Environment Setup
echo ======================================
echo.

REM Check if .env.example exists
if not exist ".env.example" (
    echo ❌ .env.example file not found!
    echo Please make sure you're running this script from the project root directory.
    pause
    exit /b 1
)

echo ℹ️  Found .env.example file
echo.

REM Create environment files
echo 📁 Creating environment files...

set "ENV_FILES=.env.development .env.production .env.beta .env.test"

for %%f in (%ENV_FILES%) do (
    if exist "%%f" (
        echo ⚠️  %%f already exists. Skipping...
    ) else (
        copy ".env.example" "%%f" >nul
        echo ✅ Created %%f
    )
)

echo.
echo 🔧 Environment files created successfully!
echo.

REM Prompt for development environment setup
echo 🛠️  Development Environment Setup
echo =================================
echo.

set /p configure_dev="Would you like to configure .env.development now? (y/n): "

if /i "%configure_dev%"=="y" (
    echo.
    echo 📝 Configuring .env.development...
    echo Please provide the following values (press Enter to keep default):
    echo.
    
    REM Database configuration
    echo 🗄️  Database Configuration:
    set /p new_db_host="Database Host [localhost]: "
    if "!new_db_host!"=="" set new_db_host=localhost
    
    set /p new_db_port="Database Port [5433]: "
    if "!new_db_port!"=="" set new_db_port=5433
    
    set /p new_db_user="Database User [postgres]: "
    if "!new_db_user!"=="" set new_db_user=postgres
    
    set /p new_db_password="Database Password [password]: "
    if "!new_db_password!"=="" set new_db_password=password
    
    set /p new_db_name="Database Name [userauth]: "
    if "!new_db_name!"=="" set new_db_name=userauth
    
    echo.
    echo 🔐 Security Configuration:
    set /p new_jwt_secret="JWT Secret (leave empty to generate): "
    if "!new_jwt_secret!"=="" (
        REM Generate a simple secret using timestamp
        for /f "tokens=2 delims==" %%I in ('wmic os get localdatetime /value') do set datetime=%%I
        set new_jwt_secret=jwt-secret-!datetime:~0,14!
        echo ℹ️  Generated JWT Secret
    )
    
    set /p new_session_secret="Session Secret (leave empty to generate): "
    if "!new_session_secret!"=="" (
        REM Generate a simple secret using timestamp
        for /f "tokens=2 delims==" %%I in ('wmic os get localdatetime /value') do set datetime=%%I
        set new_session_secret=session-secret-!datetime:~0,14!
        echo ℹ️  Generated Session Secret
    )
    
    echo.
    echo 📧 Email Configuration:
    set /p new_smtp_host="SMTP Host [smtp.gmail.com]: "
    if "!new_smtp_host!"=="" set new_smtp_host=smtp.gmail.com
    
    set /p new_smtp_port="SMTP Port [587]: "
    if "!new_smtp_port!"=="" set new_smtp_port=587
    
    set /p new_smtp_user="SMTP User: "
    set /p new_smtp_pass="SMTP Password: "
    
    echo.
    echo 🔗 OAuth Configuration:
    set /p new_google_client_id="Google Client ID: "
    set /p new_google_client_secret="Google Client Secret: "
    
    REM Create .env.development file
    (
        echo # Development Environment Configuration
        echo # Generated by setup script on %date% %time%
        echo.
        echo PORT=8080
        echo NODE_ENV=development
        echo.
        echo # Database Configuration
        echo DB_HOST=!new_db_host!
        echo DB_PORT=!new_db_port!
        echo DB_USER=!new_db_user!
        echo DB_PASSWORD=!new_db_password!
        echo DB_NAME=!new_db_name!
        echo.
        echo # Security Configuration
        echo JWT_SECRET=!new_jwt_secret!
        echo JWT_EXPIRATION=86400000
        echo SESSION_SECRET=!new_session_secret!
        echo.
        echo # Email SMTP Settings
        echo SMTP_HOST=!new_smtp_host!
        echo SMTP_PORT=!new_smtp_port!
        echo SMTP_USER=!new_smtp_user!
        echo SMTP_PASS=!new_smtp_pass!
        echo SMTP_FROM="No Reply" ^<!new_smtp_user!^>
        echo.
        echo # Application URLs
        echo APP_URL=http://localhost:3000
        echo API_URL=http://localhost:3000
        echo.
        echo # CORS Configuration
        echo CORS_ALLOWED_ORIGINS=http://localhost:3000,http://localhost:3001
        echo CORS_ALLOWED_METHODS=GET,POST,PUT,DELETE,PATCH,OPTIONS
        echo CORS_ALLOW_CREDENTIALS=true
        echo CORS_MAX_AGE=3600
        echo.
        echo # OAuth2 Configuration
        echo OAUTH2_AUTHORIZED_REDIRECT_URIS=http://localhost:8080/oauth2/callback/google,http://localhost:3000/oauth2/redirect
        echo.
        echo # Google OAuth Configuration
        echo GOOGLE_CLIENT_ID=!new_google_client_id!
        echo GOOGLE_CLIENT_SECRET=!new_google_client_secret!
        echo GOOGLE_REDIRECT_URI=http://localhost:8080/oauth2/callback/google
        echo GOOGLE_SCOPE=email,profile,openid
    ) > .env.development
    
    echo ✅ .env.development configured successfully!
)

echo.
echo 📚 Next Steps:
echo ==============
echo.
echo 1. Install dependencies:
echo    npm install
echo.
echo 2. Set up your database (PostgreSQL):
echo    docker-compose up -d  # if using Docker
echo.
echo 3. Run database migrations:
echo    npm run migration:latest
echo.
echo 4. Start the development server:
echo    npm run start:dev
echo.
echo 5. Configure other environment files as needed:
echo    - .env.production (for production deployment)
echo    - .env.beta (for staging/beta environment)
echo    - .env.test (for testing - already configured)
echo.
echo ⚠️  Remember to:
echo ⚠️  - Never commit .env files to Git
echo ⚠️  - Use strong secrets in production
echo ⚠️  - Keep your environment files secure
echo.
echo ✅ Setup completed! Happy coding! 🎉
echo.
pause
