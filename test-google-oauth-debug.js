const axios = require('axios');

const BASE_URL = 'http://localhost:8080';

async function debugGoogleOAuth() {
  console.log('🔍 Google OAuth Debug Test\n');

  try {
    // 1. Test OAuth2 Status
    console.log('1. Testing OAuth2 Status...');
    const statusResponse = await axios.get(`${BASE_URL}/oauth2/status`);
    console.log('✅ OAuth2 Status Response:');
    console.log(JSON.stringify(statusResponse.data, null, 2));
    console.log('');

    // 2. Check if redirect URI is correct
    const redirectUri = statusResponse.data.googleOAuth.redirectUri;
    console.log('2. Redirect URI Analysis:');
    console.log(`   Current Redirect URI: ${redirectUri}`);
    console.log(`   Expected for localhost: http://localhost:8080/oauth2/callback/google`);
    console.log(`   Match: ${redirectUri === 'http://localhost:8080/oauth2/callback/google' ? '✅' : '❌'}`);
    console.log('');

    // 3. Test Google Auth Initiation with detailed error handling
    console.log('3. Testing Google Auth Initiation...');
    try {
      const authResponse = await axios.get(`${BASE_URL}/auth/google`, {
        maxRedirects: 0,
        validateStatus: (status) => status === 302,
        timeout: 10000
      });
      console.log('✅ Google Auth redirect detected');
      console.log('📍 Redirect URL:', authResponse.headers.location);
      
      // Parse the redirect URL to check parameters
      const redirectUrl = new URL(authResponse.headers.location);
      console.log('   Redirect URL Analysis:');
      console.log(`   - Protocol: ${redirectUrl.protocol}`);
      console.log(`   - Host: ${redirectUrl.host}`);
      console.log(`   - Path: ${redirectUrl.pathname}`);
      console.log(`   - Client ID: ${redirectUrl.searchParams.get('client_id') ? 'Present' : 'Missing'}`);
      console.log(`   - Redirect URI: ${redirectUrl.searchParams.get('redirect_uri')}`);
      console.log(`   - Scope: ${redirectUrl.searchParams.get('scope')}`);
      console.log(`   - Response Type: ${redirectUrl.searchParams.get('response_type')}`);
      
    } catch (error) {
      if (error.response?.status === 302) {
        console.log('✅ Google Auth redirect detected (from error)');
        console.log('📍 Redirect URL:', error.response.headers.location);
      } else {
        console.log('❌ Google Auth initiation failed:');
        console.log('   Error:', error.message);
        if (error.response) {
          console.log('   Status:', error.response.status);
          console.log('   Data:', error.response.data);
        }
      }
    }
    console.log('');

    // 4. Test callback endpoint directly (this should fail but show us the error)
    console.log('4. Testing Callback Endpoint (should fail without proper OAuth flow)...');
    try {
      await axios.get(`${BASE_URL}/oauth2/callback/google?code=test&state=test`);
    } catch (error) {
      console.log('✅ Callback endpoint responded (as expected):');
      console.log('   Status:', error.response?.status);
      console.log('   Error Type:', error.response?.data?.message || error.message);
    }
    console.log('');

    // 5. Configuration Recommendations
    console.log('5. Configuration Recommendations:');
    console.log('   📋 Google Cloud Console Settings:');
    console.log('   - Authorized JavaScript origins:');
    console.log('     * http://localhost:8080');
    console.log('     * http://localhost:3000');
    console.log('   - Authorized redirect URIs:');
    console.log('     * http://localhost:8080/oauth2/callback/google');
    console.log('     * http://localhost:3000/chidhagni/oauth2/redirect');
    console.log('');
    console.log('   📋 Environment Variables Check:');
    console.log('   - GOOGLE_CLIENT_ID: Set in .env file');
    console.log('   - GOOGLE_CLIENT_SECRET: Set in .env file');
    console.log('   - APP_URL: Should be http://localhost:8080 for development');
    console.log('   - GOOGLE_REDIRECT_URI: {baseUrl}/oauth2/callback/{registrationId}');
    console.log('');

    // 6. Manual Testing Instructions
    console.log('6. Manual Testing Instructions:');
    console.log('   🌐 Open your browser and go to:');
    console.log(`   ${BASE_URL}/auth/google`);
    console.log('');
    console.log('   📱 Expected Flow:');
    console.log('   1. Redirect to Google consent screen');
    console.log('   2. Sign in with Google account');
    console.log('   3. Grant permissions');
    console.log('   4. Redirect back to your callback URL');
    console.log('   5. Receive JWT tokens and user data');
    console.log('');

  } catch (error) {
    console.error('❌ Debug test failed:', error.message);
    if (error.response) {
      console.error('   Status:', error.response.status);
      console.error('   Data:', error.response.data);
    }
  }
}

// Run the debug test
debugGoogleOAuth(); 