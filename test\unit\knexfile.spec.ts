import config from '../../src/database/knexfile';

describe('knexfile', () => {
  const OLD_ENV = process.env;

  beforeEach(() => {
    process.env = { ...OLD_ENV };
  });

  afterEach(() => {
    process.env = OLD_ENV;
  });

  it('should export a config object with development config', () => {
    expect(config).toBeDefined();
    expect(config.development).toBeDefined();
    expect(config.development.client).toBe('pg');
    expect(config.development.connection).toBeDefined();
  });

  it('should use environment variables for connection', () => {
    process.env.DB_HOST = 'localhost';
    process.env.DB_PORT = '1234';
    process.env.DB_USER = 'user';
    process.env.DB_PASSWORD = 'pass';
    process.env.DB_NAME = 'testdb';
    // Re-import to pick up new env vars
    jest.resetModules();
    const freshConfig = require('../../src/database/knexfile').default;
    expect(freshConfig.development.connection.host).toBe('localhost');
    expect(freshConfig.development.connection.port).toBe(1234);
    expect(freshConfig.development.connection.user).toBe('user');
    expect(freshConfig.development.connection.password).toBe('pass');
    expect(freshConfig.development.connection.database).toBe('testdb');
  });

  it('should default port to 5432 if not set', () => {
    delete process.env.DB_PORT;
    jest.resetModules();
    const freshConfig = require('../../src/database/knexfile').default;
    expect(freshConfig.development.connection.port).toBe(5433);
  });
}); 