import { Controller, Get, UseGuards } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { AuthService } from './auth.service';
import { AuthGuard } from './auth.guard';
import { AdminGuard } from './admin.guard';
import { AdminUserListResponse } from './dto';
import { LoggerService } from '../common/logger/logger.service';

@ApiTags('Admin')
@Controller('admin')
@UseGuards(AuthGuard, AdminGuard)
@ApiBearerAuth('bearerAuth')
export class AdminController {
  constructor(
    private readonly authService: AuthService,
    private readonly logger: LoggerService,
  ) {
    this.logger.setContext?.('AdminController');
  }

  @Get('users')
  @ApiOperation({ 
    summary: 'Get all users',
    description: 'Retrieve a list of all registered users. Admin access required.',
  })
  @ApiResponse({
    status: 200,
    description: 'Users retrieved successfully',
    type: AdminUserListResponse,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Invalid or missing JWT token',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Admin access required',
  })
  @ApiResponse({
    status: 500,
    description: 'Internal server error',
  })
  async getAllUsers(): Promise<AdminUserListResponse> {
    this.logger.log('Admin requesting all users', 'AdminController');
    return this.authService.getAllUsers();
  }
}
