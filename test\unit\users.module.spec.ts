import { Test, TestingModule } from '@nestjs/testing';
import { UsersModule } from '../../src/users/users.module';
import { UsersService } from '../../src/users/users.service';
import { DatabaseModule } from '../../src/database/database.module';
import { ConfigModule } from '@nestjs/config';

const mockKnexProvider = {
  where: jest.fn().mockReturnThis(),
  first: jest.fn().mockResolvedValue({ id: 1, email: '<EMAIL>' }),
  insert: jest.fn().mockReturnThis(),
  returning: jest.fn().mockResolvedValue([{ id: 1, email: '<EMAIL>' }]),
};
const mockKnex = jest.fn(() => mockKnexProvider);

describe('UsersModule', () => {
  let module: TestingModule;

  beforeAll(async () => {
    module = await Test.createTestingModule({
      imports: [ConfigModule.forRoot(), UsersModule, DatabaseModule],
    })
      .overrideProvider('KNEX_CONNECTION')
      .useValue(mockKnex)
      .compile();
  });

  it('should be defined', () => {
    expect(module).toBeDefined();
  });

  it('should provide UsersService', () => {
    const service = module.get<UsersService>(UsersService);
    expect(service).toBeDefined();
  });
}); 