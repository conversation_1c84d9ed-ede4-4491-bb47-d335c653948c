// WARNING: All infrastructure dependencies (DB, email, external services) must be mocked using jest.fn(). No real calls allowed in unit tests.
import 'reflect-metadata';
import { bootstrap } from '../../src/main';
import { NestFactory } from '@nestjs/core';
import { ConfigService } from '@nestjs/config';
import { Logger } from '@nestjs/common';
import * as fs from 'fs';
import { SwaggerModule } from '@nestjs/swagger';

// Mock dependencies
jest.mock('@nestjs/core');
jest.mock('@nestjs/config', () => ({
  ConfigService: jest.fn().mockImplementation(() => ({
    get: jest.fn((key: string, defaultValue?: any) => {
      if (key === 'cors') {
        return {
          allowedOrigins: ['http://localhost:3000'],
          allowedMethods: ['GET', 'POST'],
          allowCredentials: true,
          maxAge: 3600,
        };
      }
      if (key === 'PORT') {
        return 3001;
      }
      if (key === 'SESSION_SECRET') {
        return 'test-session-secret';
      }
      return defaultValue;
    }),
  })),
  registerAs: jest.fn((token, factory) => factory()),
  ConfigModule: {
    forFeature: jest.fn(),
    forRoot: jest.fn(),
  },
}));
jest.mock('fs');
jest.mock('@nestjs/swagger', () => ({
  ...jest.requireActual('@nestjs/swagger'),
  SwaggerModule: {
    createDocument: jest.fn(),
    setup: jest.fn(),
  },
}));

describe('main', () => {
  let mockApp: any;
  let mockConfigService: any;
  const spies: jest.SpyInstance[] = [];

  beforeEach(() => {
    // Reset mocks before each test
    jest.clearAllMocks();
    
    spies.push(jest.spyOn(Logger, 'log').mockImplementation(() => {}));
    spies.push(jest.spyOn(Logger, 'error').mockImplementation(() => {}));
    spies.push(jest.spyOn(Logger, 'warn').mockImplementation(() => {}));
    spies.push(jest.spyOn(Logger, 'debug').mockImplementation(() => {}));
    spies.push(jest.spyOn(Logger, 'verbose').mockImplementation(() => {}));

    mockConfigService = new ConfigService(); // Use the mocked version

    mockApp = {
      get: jest.fn().mockReturnValue(mockConfigService),
      enableCors: jest.fn(),
      useStaticAssets: jest.fn(),
      useGlobalPipes: jest.fn(),
      useGlobalFilters: jest.fn(),
      use: jest.fn(),
      init: jest.fn(),
      listen: jest.fn(),
      setGlobalPrefix: jest.fn(),
    };

    (NestFactory.create as jest.Mock).mockResolvedValue(mockApp);
    (fs.existsSync as jest.Mock).mockReturnValue(true);
  });

  afterEach(() => {
    spies.forEach(spy => spy.mockRestore());
    spies.length = 0;
  });

  it('should bootstrap the application', async () => {
    await bootstrap();

    expect(NestFactory.create).toHaveBeenCalled();
    expect(mockApp.enableCors).toHaveBeenCalled();
    expect(mockApp.useStaticAssets).toHaveBeenCalled();
    expect(mockApp.useGlobalPipes).toHaveBeenCalled();
    expect(mockApp.useGlobalFilters).toHaveBeenCalled();
    expect(mockApp.init).toHaveBeenCalled();
    expect(mockApp.listen).toHaveBeenCalledWith(3001);
  });

  it('should log an error if .env file is not found', async () => {
    (fs.existsSync as jest.Mock).mockReturnValue(false);
    await bootstrap();
    expect(Logger.error).toHaveBeenCalledWith('.env file NOT FOUND!', 'Bootstrap');
  });

  describe('setupSwagger', () => {
    it('should setup swagger successfully in development', async () => {
      (SwaggerModule.createDocument as jest.Mock).mockReturnValue({});
      process.env.NODE_ENV = 'development';
      await bootstrap();
      expect(SwaggerModule.setup).toHaveBeenCalled();
    });

    it('should handle error during swagger setup in development', async () => {
      process.env.NODE_ENV = 'development';
      const error = new Error('Swagger setup failed');
      (SwaggerModule.createDocument as jest.Mock).mockImplementation(() => {
        throw error;
      });
      await bootstrap();
      expect(Logger.error).toHaveBeenCalledWith(`Failed to setup Swagger documentation: ${error.message}`, 'Bootstrap');
      expect(Logger.warn).toHaveBeenCalledWith('Continuing without Swagger documentation', 'Bootstrap');
    });

    it('should not setup swagger if NODE_ENV is not development', async () => {
      process.env.NODE_ENV = 'production';
      await bootstrap();
      expect(SwaggerModule.setup).not.toHaveBeenCalled();
    });
  });

  describe('error handling', () => {
    it('should log error and exit if bootstrap fails in development env', async () => {
      jest.resetModules();
      jest.doMock('../../src/app.module', () => ({ AppModule: class MockAppModule {} }));
      
      const { NestFactory } = await import('@nestjs/core');
      const { Logger } = await import('@nestjs/common');

      const error = new Error('Bootstrap failed');
      (NestFactory.create as jest.Mock).mockRejectedValue(error);
      const mockExit = jest.spyOn(process, 'exit').mockImplementation((() => {}) as any);
      const errorSpy = jest.spyOn(Logger, 'error').mockImplementation(() => {});
      process.env.NODE_ENV = 'development';
  
      await import('../../src/main');
  
      await new Promise(resolve => setImmediate(resolve));
  
      expect(errorSpy).toHaveBeenCalledWith(`Failed to start application: ${error.message}`, 'Bootstrap');
      expect(mockExit).toHaveBeenCalledWith(1);
      
      errorSpy.mockRestore();
      mockExit.mockRestore();
    });

    it('should log error and not exit if bootstrap fails in non-development env', async () => {
      jest.resetModules();
      jest.doMock('../../src/app.module', () => ({ AppModule: class MockAppModule {} }));

      const { NestFactory } = await import('@nestjs/core');
      const { Logger } = await import('@nestjs/common');

      const error = new Error('Bootstrap failed');
      (NestFactory.create as jest.Mock).mockRejectedValue(error);
      const mockExit = jest.spyOn(process, 'exit').mockImplementation((() => {}) as any);
      const errorSpy = jest.spyOn(Logger, 'error').mockImplementation(() => {});
      process.env.NODE_ENV = 'production';
      
      try {
        await import('../../src/main');
      } catch (e) {
        // We expect the promise to reject, but the catch block in main.ts should handle it.
      }
      
      await new Promise(resolve => setImmediate(resolve));
      
      expect(errorSpy).toHaveBeenCalledWith(`Failed to start application: ${error.message}`, 'Bootstrap');
      expect(mockExit).not.toHaveBeenCalled();

      errorSpy.mockRestore();
      mockExit.mockRestore();
    });
  });
}); 