import { Injectable, ServiceUnavailableException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

@Injectable()
export class SendGridService {
  private isInitialized = false;
  private sgMail: any = null;

  constructor(private readonly configService: ConfigService) {
    this.initializeSendGrid();
  }

  private async initializeSendGrid(): Promise<void> {
    const apiKey = this.configService.get<string>('email.sendgridApiKey');
    
    if (!apiKey) {
      console.log('[SENDGRID] API key not configured - SendGrid service not available');
      return;
    }

    try {
      // Check if SendGrid package is available
      let sgMailModule: any;
      try {
        // Use require instead of import to avoid TypeScript issues
        sgMailModule = require('@sendgrid/mail');
      } catch (requireError) {
        console.log('[SENDGRID] SendGrid package not installed - service not available');
        console.log('[SENDGRID] To use SendGrid, run: npm install @sendgrid/mail');
        return;
      }

      this.sgMail = sgMailModule;
      this.sgMail.setApiKey(apiKey);
      this.isInitialized = true;
      console.log('[SENDGRID] Initialized successfully');
    } catch (error) {
      console.error('[SENDGRID] Failed to initialize:', error);
    }
  }

  async sendEmail(
    to: string,
    subject: string,
    htmlContent: string,
    from?: string
  ): Promise<void> {
    if (!this.isInitialized || !this.sgMail) {
      throw new ServiceUnavailableException('SendGrid is not properly configured or package not installed');
    }

    const fromEmail = from || this.configService.get<string>('email.sendgridFrom');

    try {
      const msg = {
        to,
        from: fromEmail,
        subject,
        html: htmlContent,
      };

      await this.sgMail.send(msg);
      console.log(`[SENDGRID SUCCESS] Email sent successfully to ${to}`);
    } catch (error) {
      console.error('[SENDGRID ERROR] Failed to send email:', error);
      throw new ServiceUnavailableException('Failed to send email via SendGrid');
    }
  }

  isAvailable(): boolean {
    return this.isInitialized;
  }
} 