// WARNING: All infrastructure dependencies (DB, email, external services) must be mocked using jest.fn(). No real calls allowed in unit tests.
import { JwtStrategy } from '../../src/auth/jwt.strategy';
import { ConfigService } from '@nestjs/config';

describe('JwtStrategy', () => {
  let strategy: JwtStrategy;
  let configService: any;

  beforeEach(() => {
    configService = { get: jest.fn().mockReturnValue('secret') };
    strategy = new JwtStrategy(configService);
  });

  it('should be defined', () => {
    expect(strategy).toBeDefined();
  });

  it('should call configService.get with JWT_SECRET', () => {
    expect(configService.get).toHaveBeenCalledWith('JWT_SECRET');
  });

  it('validate should return user object', async () => {
    const payload = { sub: 'user-id', email: '<EMAIL>' };
    const result = await strategy.validate(payload);
    expect(result).toEqual({ userId: 'user-id', email: '<EMAIL>' });
  });
}); 