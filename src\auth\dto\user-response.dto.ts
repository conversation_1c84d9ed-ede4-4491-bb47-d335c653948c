import { ApiProperty } from '@nestjs/swagger';

export class UserResponse {
  @ApiProperty({
    description: 'User unique identifier',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  id!: string;

  @ApiProperty({
    description: 'User email address',
    example: '<EMAIL>',
  })
  email!: string;

  @ApiProperty({
    description: 'User full name',
    example: '<PERSON>',
  })
  name!: string;

  @ApiProperty({
    description: 'User mobile number',
    example: '**********',
  })
  mobileNumber!: string;

  @ApiProperty({
    description: 'User role',
    example: 'USER',
    enum: ['USER', 'ADMIN'],
  })
  role!: string;

  @ApiProperty({
    description: 'Whether the user account is active',
    example: true,
  })
  isActive!: boolean;

  @ApiProperty({
    description: 'Whether the user email is verified',
    example: true,
  })
  isEmailVerified!: boolean;

  @ApiProperty({
    description: 'Whether the user account is locked',
    example: false,
  })
  isAccountLocked!: boolean;

  @ApiProperty({
    description: 'Social login provider if applicable',
    example: 'google',
    nullable: true,
  })
  socialLoginProvider?: string;

  @ApiProperty({
    description: 'User creation timestamp',
    example: '2024-01-01T12:00:00Z',
  })
  createdOn!: Date;

  @ApiProperty({
    description: 'User last update timestamp',
    example: '2024-01-01T12:00:00Z',
  })
  updatedOn!: Date;

  @ApiProperty({
    description: 'Last login timestamp',
    example: '2024-01-01T12:00:00Z',
    nullable: true,
  })
  lastLoginAt?: Date;
}
