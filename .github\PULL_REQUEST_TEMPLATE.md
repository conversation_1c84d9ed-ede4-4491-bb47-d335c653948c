## The PR includes:
- [ ] Provide summary of the backend changes


## Description

Briefly describe the purpose of this change and any relevant context. What problem does it solve? What feature does it add?


## Technical Details

Summarize what was changed in the codebase. List affected modules, services, controllers, or configuration files. Mention any new dependencies or breaking changes.


## Checklist

- [ ] Application builds successfully (`npm run build`)
- [ ] Unit and integration tests pass (`npm test`)
- [ ] Database migrations (Knex) are included and tested (if applicable)
- [ ] No hardcoded credentials or sensitive information in the codebase
- [ ] Logging is appropriate and secure
- [ ] SonarQube quality gate passed (if applicable)
- [ ] Semgrep scan completed with no high or critical issues


## Related Issue / Ticket

Add a link or reference to the relevant issue, task, or ticket.

---

## Additional Notes

Include any setup instructions, testing notes, or reviewer guidance.