import { IsString, Length } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class ResetPasswordDto {
  @ApiProperty({ 
    description: 'Reset token',
    example: 'abcdef123456'
  })
  @IsString()
  @Length(1)
  resetToken!: string;

  @ApiProperty({ 
    description: 'New password',
    example: 'newpassword123'
  })
  @IsString()
  @Length(8, 255)
  newPassword!: string;

  @ApiProperty({ 
    description: 'User IP address',
    example: '*********'
  })
  @IsString()
  @Length(1)
  ipAddress!: string;

  @ApiProperty({ 
    description: 'Device details',
    example: 'Chrome 120.0.0.0 on Windows 10'
  })
  @IsString()
  @Length(1)
  deviceDetails!: string;
} 