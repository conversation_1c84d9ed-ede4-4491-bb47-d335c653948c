module.exports = {
  preset: 'ts-jest',
  testEnvironment: 'node',
  moduleFileExtensions: ['js', 'json', 'ts'],
  rootDir: '.',
  testRegex: '.*\\.spec\\.ts$',
  transform: {
    '^.+\\.(t|j)s$': 'ts-jest',
  },
  setupFiles: ['<rootDir>/test/setup-env.ts'],
  collectCoverageFrom: [
    "src/**/*.{js,ts}",
    "!src/**/*.d.ts"
  ],
  testPathIgnorePatterns: [
    '/node_modules/',
    'test/google-oauth.*',
    'test/google.strategy.*',
    'test/google-oauth.config.*',
  ],
  coveragePathIgnorePatterns: [
    '/node_modules/',
    'test/google-oauth.*',
    'test/google.strategy.*',
    'test/google-oauth.config.*',
  ],
  coverageThreshold: {
    global: {
      statements: 80,
      branches: 67,
      functions: 70,
      lines: 79,
    },
  },
  coverageReporters: ['text', 'lcov', 'html'],
}; 