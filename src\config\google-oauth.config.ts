import { registerAs } from '@nestjs/config';

export default registerAs('googleOAuth', () => {
  // Handle the redirect URI with placeholders
  let redirectUri = process.env.GOOGLE_REDIRECT_URI ?? 'http://localhost:8080/oauth2/callback/google';
  
  // Replace placeholders if they exist
  if (redirectUri.includes('{baseUrl}')) {
    let baseUrl = process.env.APP_URL ?? 'http://localhost:8080';
    
    // Force http for localhost development to avoid SSL issues
    if (baseUrl.includes('localhost') || baseUrl.includes('127.0.0.1')) {
      baseUrl = baseUrl.replace('https://', 'http://');
    }
    
    redirectUri = redirectUri.replace('{baseUrl}', baseUrl);
  }
  
  if (redirectUri.includes('{registrationId}')) {
    redirectUri = redirectUri.replace('{registrationId}', 'google');
  }

  // Clean up authorized redirect URIs
  const authorizedRedirectUris = (process.env.OAUTH2_AUTHORIZED_REDIRECT_URIS ?? 'http://localhost:3000/chidhagni/oauth2/redirect,myandroidapp://oauth2/redirect,myiosapp://oauth2/redirect')
    .split(',')
    .map(uri => uri.trim())
    .filter(uri => uri.length > 0);

  // Add the callback URI to authorized redirects if not already present
  if (!authorizedRedirectUris.includes(redirectUri)) {
    authorizedRedirectUris.push(redirectUri);
  }

  // Debug logging
  console.log('🔧 Google OAuth Configuration:');
  console.log('   Client ID:', process.env.GOOGLE_CLIENT_ID ? 'Configured' : 'Missing');
  console.log('   Client Secret:', process.env.GOOGLE_CLIENT_SECRET ? 'Configured' : 'Missing');
  console.log('   Redirect URI:', redirectUri);
  console.log('   Scope:', process.env.GOOGLE_SCOPE ?? 'email,profile,openid');
  console.log('   Authorized Redirect URIs:', authorizedRedirectUris);
  console.log('   APP_URL from env:', process.env.APP_URL);

  return {
    clientId: process.env.GOOGLE_CLIENT_ID,
    clientSecret: process.env.GOOGLE_CLIENT_SECRET,
    redirectUri,
    scope: process.env.GOOGLE_SCOPE ?? 'email,profile,openid',
    authorizedRedirectUris,
  };
}); 