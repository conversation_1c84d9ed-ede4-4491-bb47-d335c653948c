import googleOAuthConfig from '../../src/config/google-oauth.config';

describe('googleOAuthConfig', () => {
  const OLD_ENV = process.env;

  beforeEach(() => {
    jest.resetModules();
    process.env = { ...OLD_ENV };
  });

  afterEach(() => {
    process.env = OLD_ENV;
  });

  it('should use default values when env vars are not set', () => {
    delete process.env.GOOGLE_CLIENT_ID;
    delete process.env.GOOGLE_CLIENT_SECRET;
    delete process.env.GOOGLE_REDIRECT_URI;
    delete process.env.GOOGLE_SCOPE;
    delete process.env.OAUTH2_AUTHORIZED_REDIRECT_URIS;

    const config = googleOAuthConfig();
    expect(config.clientId).toBeUndefined();
    expect(config.clientSecret).toBeUndefined();
    expect(config.redirectUri).toBe('http://localhost:8080/oauth2/callback/google');
    expect(config.scope).toBe('email,profile,openid');
    expect(config.authorizedRedirectUris).toEqual(
      expect.arrayContaining([
        'http://localhost:3000/chidhagni/oauth2/redirect',
        'myandroidapp://oauth2/redirect',
        'myiosapp://oauth2/redirect',
        'http://localhost:8080/oauth2/callback/google',
      ])
    );
  });

  it('should use environment variables when set', () => {
    process.env.GOOGLE_CLIENT_ID = 'test-client-id';
    process.env.GOOGLE_CLIENT_SECRET = 'test-client-secret';
    process.env.GOOGLE_REDIRECT_URI = 'http://test-redirect';
    process.env.GOOGLE_SCOPE = 'openid';
    process.env.OAUTH2_AUTHORIZED_REDIRECT_URIS = 'http://test-redirect-uri';

    const config = googleOAuthConfig();
    expect(config.clientId).toBe('test-client-id');
    expect(config.clientSecret).toBe('test-client-secret');
    expect(config.redirectUri).toBe('http://test-redirect');
    expect(config.scope).toBe('openid');
    expect(config.authorizedRedirectUris).toEqual(
      expect.arrayContaining([
        'http://test-redirect-uri',
        'http://test-redirect',
      ])
    );
  });

  it('should fallback to default for each individual env var if not set', () => {
    process.env.GOOGLE_CLIENT_ID = 'test-client-id';
    delete process.env.GOOGLE_CLIENT_SECRET;
    process.env.GOOGLE_REDIRECT_URI = 'http://test-redirect';
    delete process.env.GOOGLE_SCOPE;
    process.env.OAUTH2_AUTHORIZED_REDIRECT_URIS = 'http://test-redirect-uri';

    const config = googleOAuthConfig();
    expect(config.clientId).toBe('test-client-id');
    expect(config.clientSecret).toBeUndefined();
    expect(config.redirectUri).toBe('http://test-redirect');
    expect(config.scope).toBe('email,profile,openid');
    expect(config.authorizedRedirectUris).toEqual(
      expect.arrayContaining([
        'http://test-redirect-uri',
        'http://test-redirect',
      ])
    );
  });

  it('should replace {baseUrl} and {registrationId} in redirectUri and force http for localhost', () => {
    process.env.GOOGLE_REDIRECT_URI = '{baseUrl}/oauth2/callback/{registrationId}';
    process.env.APP_URL = 'https://localhost:3000';
    process.env.OAUTH2_AUTHORIZED_REDIRECT_URIS = 'http://localhost:3000/chidhagni/oauth2/redirect';

    const config = googleOAuthConfig();
    expect(config.redirectUri).toBe('http://localhost:3000/oauth2/callback/google');
    expect(config.authorizedRedirectUris).toContain('http://localhost:3000/oauth2/callback/google');
  });

  it('should replace {baseUrl} with 127.0.0.1 and force http', () => {
    process.env.GOOGLE_REDIRECT_URI = '{baseUrl}/oauth2/callback/{registrationId}';
    process.env.APP_URL = 'https://127.0.0.1:8080';
    process.env.OAUTH2_AUTHORIZED_REDIRECT_URIS = 'http://localhost:3000/chidhagni/oauth2/redirect';

    const config = googleOAuthConfig();
    expect(config.redirectUri).toBe('http://127.0.0.1:8080/oauth2/callback/google');
    expect(config.authorizedRedirectUris).toContain('http://127.0.0.1:8080/oauth2/callback/google');
  });

  it('should not duplicate redirectUri in authorizedRedirectUris', () => {
    process.env.GOOGLE_REDIRECT_URI = 'http://localhost:3000/chidhagni/oauth2/redirect';
    process.env.OAUTH2_AUTHORIZED_REDIRECT_URIS = 'http://localhost:3000/chidhagni/oauth2/redirect';

    const config = googleOAuthConfig();
    // Should only appear once
    expect(config.authorizedRedirectUris.filter(uri => uri === 'http://localhost:3000/chidhagni/oauth2/redirect').length).toBe(1);
  });

  it('should filter out empty/whitespace URIs from authorizedRedirectUris', () => {
    process.env.OAUTH2_AUTHORIZED_REDIRECT_URIS = 'http://localhost:3000/chidhagni/oauth2/redirect,   , ,myandroidapp://oauth2/redirect';
    process.env.GOOGLE_REDIRECT_URI = 'http://localhost:8080/oauth2/callback/google';

    const config = googleOAuthConfig();
    expect(config.authorizedRedirectUris).not.toContain('');
    expect(config.authorizedRedirectUris).not.toContain('   ');
    expect(config.authorizedRedirectUris).toContain('myandroidapp://oauth2/redirect');
  });
}); 