name: SonarQube Analysis

on:
  workflow_call:

jobs:
  sonarqube:
    name: SonarQube Analysis
    runs-on: [self-hosted, linux]
    steps:
      - uses: actions/checkout@v3
      
      - name: Set up Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '20'
          
      - name: Download Coverage Report
        uses: actions/download-artifact@v4
        with:
          name: coverage-report
          path: coverage
          
      - name: Install Node.js dependencies
        run: npm install

      - name: SonarQube Scan
        env:
          SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}
          SONAR_HOST_URL: http://***************:9000
        run: |
          # Run SonarQube analysis using the npm script
          npm run sonar -- \
            -Dsonar.host.url=$SONAR_HOST_URL \
            -Dsonar.token=$SONAR_TOKEN
