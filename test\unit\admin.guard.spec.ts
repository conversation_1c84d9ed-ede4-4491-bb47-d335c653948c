import { Test, TestingModule } from '@nestjs/testing';
import { ExecutionContext, ForbiddenException } from '@nestjs/common';
import { AdminGuard } from '../../src/auth/admin.guard';
import { Knex } from 'knex';

describe('AdminGuard', () => {
  let guard: AdminGuard;
  let knexMock: jest.Mocked<Knex>;
  let queryBuilder: any;

  beforeEach(async () => {
    queryBuilder = {
      where: jest.fn().mockReturnThis(),
      first: jest.fn(),
    };

    knexMock = jest.fn(() => queryBuilder) as any;

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        AdminGuard,
        {
          provide: 'KNEX_CONNECTION',
          useValue: knexMock,
        },
      ],
    }).compile();

    guard = module.get<AdminGuard>(AdminGuard);
  });

  const createMockExecutionContext = (user: any): ExecutionContext => {
    return {
      switchToHttp: () => ({
        getRequest: () => ({ user }),
      }),
    } as ExecutionContext;
  };

  it('should be defined', () => {
    expect(guard).toBeDefined();
  });

  describe('canActivate', () => {
    it('should allow access for admin user', async () => {
      const mockUser = { userId: 'admin-user-id' };
      const mockDbUser = { id: 'admin-user-id', role: 'ADMIN' };
      
      queryBuilder.first.mockResolvedValue(mockDbUser);
      const context = createMockExecutionContext(mockUser);

      const result = await guard.canActivate(context);

      expect(result).toBe(true);
      expect(knexMock).toHaveBeenCalledWith('users');
      expect(queryBuilder.where).toHaveBeenCalledWith({ id: 'admin-user-id' });
    });

    it('should deny access for regular user', async () => {
      const mockUser = { userId: 'regular-user-id' };
      const mockDbUser = { id: 'regular-user-id', role: 'USER' };
      
      queryBuilder.first.mockResolvedValue(mockDbUser);
      const context = createMockExecutionContext(mockUser);

      await expect(guard.canActivate(context)).rejects.toThrow(
        new ForbiddenException('Admin access required')
      );
    });

    it('should deny access when user is not authenticated', async () => {
      const context = createMockExecutionContext(null);

      await expect(guard.canActivate(context)).rejects.toThrow(
        new ForbiddenException('User not authenticated')
      );
    });

    it('should deny access when user has no userId', async () => {
      const mockUser = {};
      const context = createMockExecutionContext(mockUser);

      await expect(guard.canActivate(context)).rejects.toThrow(
        new ForbiddenException('User not authenticated')
      );
    });

    it('should deny access when user is not found in database', async () => {
      const mockUser = { userId: 'non-existent-user-id' };
      
      queryBuilder.first.mockResolvedValue(null);
      const context = createMockExecutionContext(mockUser);

      await expect(guard.canActivate(context)).rejects.toThrow(
        new ForbiddenException('User not found')
      );
    });

    it('should handle database errors', async () => {
      const mockUser = { userId: 'user-id' };
      const dbError = new Error('Database connection failed');
      
      queryBuilder.first.mockRejectedValue(dbError);
      const context = createMockExecutionContext(mockUser);

      await expect(guard.canActivate(context)).rejects.toThrow('Database connection failed');
    });
  });
});
