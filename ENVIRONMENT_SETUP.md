# 🔧 Environment Setup Guide

This guide explains how to set up environment variables for the AI Nest Backend application.

## 📋 Table of Contents

- [Quick Start](#-quick-start)
- [Manual Setup](#-manual-setup)
- [Environment Files](#-environment-files)
- [Required Variables](#-required-variables)
- [Security Best Practices](#-security-best-practices)
- [Troubleshooting](#-troubleshooting)

## 🚀 Quick Start

### Option 1: Automated Setup (Recommended)

Run the setup script to automatically create and configure environment files:

```bash
# For all platforms
npm run setup

# Or run directly:
# Linux/macOS
bash setup.sh

# Windows
setup.bat
```

### Option 2: Manual Setup

If you prefer to set up manually, follow the [Manual Setup](#-manual-setup) section below.

## 🛠️ Manual Setup

### 1. Clone the Repository

```bash
git clone <your-repository-url>
cd ai-nest-backend
```

### 2. Create Environment Files

Copy the example file to create your environment files:

```bash
# Create all environment files
cp .env.example .env.development
cp .env.example .env.production
cp .env.example .env.beta
cp .env.example .env.test
```

### 3. Configure Each Environment

Edit each file with your specific configuration:

```bash
# Edit development environment
nano .env.development  # or use your preferred editor

# Edit production environment
nano .env.production

# Edit beta/staging environment
nano .env.beta

# Test environment is pre-configured
```

## 📁 Environment Files

| File | Purpose | Git Tracked |
|------|---------|-------------|
| `.env.example` | Template with all required variables | ✅ Yes |
| `.env.development` | Local development configuration | ❌ No |
| `.env.production` | Production deployment configuration | ❌ No |
| `.env.beta` | Staging/beta environment configuration | ❌ No |
| `.env.test` | Testing configuration (pre-configured) | ❌ No |

## 🔑 Required Variables

### Application Configuration
```bash
PORT=8080                    # Server port
NODE_ENV=development         # Environment (development/production/beta/test)
```

### Database Configuration
```bash
DB_HOST=localhost           # Database host
DB_PORT=5433               # Database port
DB_USER=postgres           # Database username
DB_PASSWORD=your_password  # Database password
DB_NAME=userauth          # Database name
```

### Security Configuration
```bash
JWT_SECRET=your-strong-jwt-secret-here        # JWT signing secret
JWT_EXPIRATION=86400000                       # JWT expiration (24 hours)
SESSION_SECRET=your-strong-session-secret-here # Session secret
```

### Email Configuration
```bash
SMTP_HOST=smtp.gmail.com                      # SMTP server host
SMTP_PORT=587                                 # SMTP server port
SMTP_USER=<EMAIL>               # SMTP username
SMTP_PASS=your-app-password                  # SMTP password
SMTP_FROM="No Reply" <<EMAIL>>  # From address
```

### Application URLs
```bash
APP_URL=http://localhost:3000                 # Frontend URL
API_URL=http://localhost:3000                 # API base URL
```

### CORS Configuration
```bash
CORS_ALLOWED_ORIGINS=http://localhost:3000,http://localhost:3001
CORS_ALLOWED_METHODS=GET,POST,PUT,DELETE,PATCH,OPTIONS
CORS_ALLOW_CREDENTIALS=true
CORS_MAX_AGE=3600
```

### OAuth2 Configuration
```bash
OAUTH2_AUTHORIZED_REDIRECT_URIS=http://localhost:8080/oauth2/callback/google,http://localhost:3000/oauth2/redirect

# Google OAuth
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret
GOOGLE_REDIRECT_URI=http://localhost:8080/oauth2/callback/google
GOOGLE_SCOPE=email,profile,openid
```

## 🔒 Security Best Practices

### 1. Strong Secrets
Generate cryptographically secure secrets:

```bash
# Generate JWT secret
openssl rand -base64 32

# Generate session secret
openssl rand -base64 32
```

### 2. Environment-Specific Configuration

| Environment | Security Level | Example Secrets |
|-------------|---------------|-----------------|
| **Development** | Low | Simple, readable secrets |
| **Beta/Staging** | Medium | Generated secrets, test data |
| **Production** | High | Strong, unique secrets |

### 3. Secret Management

For production deployments, consider using:
- **AWS Secrets Manager**
- **Azure Key Vault**
- **Google Secret Manager**
- **HashiCorp Vault**
- **Kubernetes Secrets**

### 4. Never Commit Secrets

✅ **DO:**
- Use environment variables
- Keep `.env.*` files local
- Use `.env.example` for documentation
- Rotate secrets regularly

❌ **DON'T:**
- Commit `.env` files to Git
- Share secrets in chat/email
- Use weak or default secrets
- Hardcode secrets in source code

## 🚀 Getting Started After Setup

### 1. Install Dependencies
```bash
npm install
```

### 2. Set Up Database
```bash
# Using Docker (recommended for development)
docker-compose up -d

# Or set up PostgreSQL manually
```

### 3. Run Migrations
```bash
npm run migration:latest
```

### 4. Start Development Server
```bash
npm run start:dev
```

### 5. Verify Setup
Visit `http://localhost:8080/health` to verify the application is running.

## 🔍 Troubleshooting

### Common Issues

#### 1. "SESSION_SECRET environment variable is required but not set"
**Solution:** Make sure your environment file contains `SESSION_SECRET=your-secret-here`

#### 2. "Cannot connect to database"
**Solutions:**
- Check database is running: `docker-compose ps`
- Verify database credentials in your `.env` file
- Ensure database exists: `createdb userauth`

#### 3. "Google OAuth configuration missing"
**Solutions:**
- Set up Google OAuth in [Google Cloud Console](https://console.cloud.google.com/)
- Add `GOOGLE_CLIENT_ID` and `GOOGLE_CLIENT_SECRET` to your `.env` file

#### 4. Environment file not found
**Solutions:**
- Run the setup script: `npm run setup`
- Manually copy: `cp .env.example .env.development`
- Check you're in the project root directory

### Debug Commands

```bash
# Check environment loading
npm run start:dev

# Test database connection
npm run migration:latest

# Run tests
npm test

# Check configuration
curl http://localhost:8080/oauth2/debug
```

## 📞 Support

If you encounter issues:

1. Check this documentation
2. Review the `.env.example` file
3. Run the setup script again
4. Check application logs
5. Contact the development team

## 🔄 Updating Environment Configuration

When new environment variables are added:

1. Update `.env.example` with new variables
2. Update this documentation
3. Notify team members to update their local `.env` files
4. Update deployment configurations

---

**Remember:** Keep your environment files secure and never commit them to version control! 🔒
