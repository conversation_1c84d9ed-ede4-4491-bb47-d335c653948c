# Clean Architecture Guidelines for ai-nest-backend

## 1. Overview
Clean Architecture separates software solutions into layers, ensuring independence of frameworks, UI, database, and external agencies. This makes the codebase easier to test, maintain, and extend.

## 2. Layered Structure

### a. Domain Layer (Entities)
- Contains business models and core business logic.
- No dependencies on frameworks or external libraries.
- Example: `User`, `EmailVerification` classes.

### b. Application Layer (Use Cases)
- Contains application-specific business rules and orchestrates domain entities.
- No dependencies on frameworks.
- Example: `RegisterUser`, `SendVerificationEmail` use cases.

### c. Interface Adapters
- Adapts data between the application/domain layers and external agents (web, DB, etc).
- Includes controllers, DTOs, mappers, and repository interfaces.
- Example: NestJS controllers, DTOs, repository interfaces.

### d. Frameworks & Drivers (Infrastructure)
- Contains actual implementations for database, email, external APIs, etc.
- Includes NestJS modules, database providers, and third-party integrations.

## 3. Dependency Rule
- Code dependencies always point inwards.
- Outer layers (controllers, infrastructure) depend on inner layers (use cases, domain), never the reverse.
- Inner layers must not import from outer layers.

## 4. Folder Structure Example

```
src/
  domain/           # Entities, business logic (pure TS)
  use-cases/        # Application logic (pure TS)
  adapters/         # Controllers, DTOs, mappers, repository interfaces
  infrastructure/   # DB, email, external APIs, NestJS modules
  main.ts           # App bootstrap
```

## 5. Best Practices
- **No business logic in controllers or infrastructure.**
- **DTOs are for data transfer only, not business logic.**
- **Repositories are interfaces in core, implemented in infrastructure.**
- **Services should be split: use-cases (pure logic) vs. infrastructure (external calls).**
- **Write tests for use-cases and domain logic without needing NestJS.**
- **Keep framework-specific code at the edges.**

## 6. Example Mapping (Current Project)
- `src/auth/`, `src/users/`, `src/email/`: Mix of adapters and use-cases. Refactor as needed.
- `src/database/`: Infrastructure.
- DTOs: Interface adapters.
- Services: Split into use-cases (pure logic) and infrastructure (external calls).

## 7. References
- [Clean Architecture by Uncle Bob](https://8thlight.com/blog/uncle-bob/2012/08/13/the-clean-architecture.html)
- [NestJS Clean Architecture Example](https://dev.to/nestjs/clean-architecture-in-nestjs-1b4l)

---

**Follow these guidelines for all new development and refactoring.** 