import { registerAs } from '@nestjs/config';

export const emailConfig = registerAs('email', () => ({
  // Email provider type: 'smtp' | 'sendgrid'
  provider: process.env.EMAIL_PROVIDER || 'smtp',
  
  // SMTP configuration (for Gmail, etc.)
  host: process.env.SMTP_HOST,
  port: parseInt(process.env.SMTP_PORT ?? '587', 10),
  user: process.env.SMTP_USER,
  pass: process.env.SMTP_PASS,
  from: process.env.SMTP_FROM ?? '<EMAIL>',
  
  // SendGrid configuration
  sendgridApiKey: process.env.SENDGRID_API_KEY,
  sendgridFrom: process.env.SENDGRID_FROM || process.env.SMTP_FROM || '<EMAIL>',
})); 