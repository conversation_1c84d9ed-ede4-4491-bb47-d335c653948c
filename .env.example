# Environment Configuration Example
# Copy this file to .env.development, .env.production, etc. and fill in your values

# Application Configuration
PORT=8080
NODE_ENV=development

# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_USER=your_database_user
DB_PASSWORD=your_database_password
DB_NAME=your_database_name

# JWT Configuration
JWT_SECRET=your-strong-jwt-secret-here
JWT_EXPIRATION=86400000

# Session Configuration
SESSION_SECRET=your-strong-session-secret-here

# Email SMTP Settings
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password
SMTP_FROM='"No Reply" <<EMAIL>>'

# Application URLs
APP_URL=http://localhost:3000
API_URL=http://localhost:3000

# CORS Configuration
CORS_ALLOWED_ORIGINS=http://localhost:3000,http://localhost:3001
CORS_ALLOWED_METHODS=GET,POST,PUT,DELETE,PATCH,OPTIONS
CORS_ALLOW_CREDENTIALS=true
CORS_MAX_AGE=3600

# OAuth2 Configuration
OAUTH2_AUTHORIZED_REDIRECT_URIS=http://localhost:8080/oauth2/callback/google,http://localhost:3000/oauth2/redirect

# Google OAuth Configuration
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret
GOOGLE_REDIRECT_URI=http://localhost:8080/oauth2/callback/google
GOOGLE_SCOPE=email,profile,openid
