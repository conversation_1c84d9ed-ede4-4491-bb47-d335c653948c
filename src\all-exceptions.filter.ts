import {
  ExceptionFilter,
  Catch,
  ArgumentsHost,
  HttpException,
  HttpStatus,
} from '@nestjs/common';
import { Request, Response } from 'express';

@Catch()
export class AllExceptionsFilter implements ExceptionFilter {
  catch(exception: unknown, host: ArgumentsHost) {
    const ctx = host.switchToHttp();
    const response = ctx.getResponse<Response>();
    const request = ctx.getRequest<Request>();

    if (exception instanceof HttpException) {
      const status = exception.getStatus();
      const res = exception.getResponse();
      const message = typeof res === 'string' ? res : (res as any).message ?? exception.message;
      const errorResponse = {
        statusCode: status,
        message,
        error: exception.name,
        timestamp: new Date().toISOString(),
        path: request.url,
      } as any;

      if (process.env.NODE_ENV === 'development' && exception instanceof Error) {
        errorResponse.stack = exception.stack;
      }
      response.status(status).json(errorResponse);
    } else {
      // For all other errors (Error or non-Error), return 400
      const status = HttpStatus.BAD_REQUEST;
      let message: string;
      let error: string;
      if (typeof exception === 'string') {
        message = exception;
        error = 'UnknownException';
      } else if (exception instanceof Error) {
        message = exception.message;
        error = exception.name;
      } else {
        message = JSON.stringify(exception);
        error = 'UnknownException';
      }
      const errorResponse: any = {
        statusCode: status,
        message,
        error,
        timestamp: new Date().toISOString(),
        path: request.url,
      };

      if (process.env.NODE_ENV === 'development' && exception instanceof Error) {
        errorResponse.stack = exception.stack;
      }
      response.status(status).json(errorResponse);
    }
  }
} 