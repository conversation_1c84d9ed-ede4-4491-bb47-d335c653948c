import { Injectable } from '@nestjs/common';
import { PassportStrategy } from '@nestjs/passport';
import { Strategy, VerifyCallback } from 'passport-google-oauth20';
import { ConfigService } from '@nestjs/config';

@Injectable()
export class GoogleStrategy extends PassportStrategy(Strategy, 'google') {
  constructor(private readonly configService: ConfigService) {
    const googleConfig = configService.get('googleOAuth');
    
    super({
      clientID: googleConfig.clientId,
      clientSecret: googleConfig.clientSecret,
      callbackURL: googleConfig.redirectUri,
      scope: googleConfig.scope.split(',').map((s: string) => s.trim()),
      passReqToCallback: false,
    });
  }

  async validate(
    accessToken: string,
    refreshToken: string,
    profile: any,
    done: VerifyCallback,
  ): Promise<any> {
    try {
      const { name, emails, photos } = profile;
      
      if (!emails || emails.length === 0) {
        return done(new Error('No email found in Google profile'), undefined);
      }

      const user = {
        email: emails[0].value,
        firstName: name?.givenName ?? '',
        lastName: name?.familyName ?? '',
        picture: photos?.[0]?.value ?? '',
        accessToken,
        googleId: profile.id,
      };
      
      done(null, user);
    } catch (error) {
      done(error, undefined);
    }
  }
} 