import { AllExceptionsFilter } from '../../src/all-exceptions.filter';
import { ArgumentsHost, HttpException, HttpStatus } from '@nestjs/common';
import { Request, Response } from 'express';

describe('AllExceptionsFilter', () => {
  let filter: AllExceptionsFilter;
  let mockResponse: Partial<Response>;
  let mockRequest: Partial<Request>;
  let mockArgumentsHost: ArgumentsHost;
  let statusCode: number | undefined;
  let jsonResponse: any;

  beforeEach(() => {
    filter = new AllExceptionsFilter();
    statusCode = undefined;
    jsonResponse = undefined;
    mockResponse = {
      status: jest.fn().mockImplementation((code) => {
        statusCode = code;
        return mockResponse;
      }),
      json: jest.fn().mockImplementation((json) => {
        jsonResponse = json;
        return json;
      }),
    } as any;
    mockRequest = {
      url: '/test-url',
    } as any;
    mockArgumentsHost = {
      switchToHttp: () => ({
        getResponse: () => mockResponse,
        getRequest: () => mockRequest,
      }),
    } as any;
  });

  it('should handle HttpException and return correct structure', () => {
    const exception = new HttpException('Forbidden', HttpStatus.FORBIDDEN);
    filter.catch(exception, mockArgumentsHost);
    expect(statusCode).toBe(HttpStatus.FORBIDDEN);
    expect(jsonResponse).toMatchObject({
      statusCode: HttpStatus.FORBIDDEN,
      message: 'Forbidden',
      error: 'HttpException',
      path: '/test-url',
      timestamp: expect.any(String),
    });
  });

  it('should handle generic Error and return correct structure', () => {
    const exception = new Error('Something went wrong');
    filter.catch(exception, mockArgumentsHost);
    expect(statusCode).toBe(HttpStatus.BAD_REQUEST);
    expect(jsonResponse).toMatchObject({
      statusCode: HttpStatus.BAD_REQUEST,
      message: 'Something went wrong',
      error: 'Error',
      path: '/test-url',
      timestamp: expect.any(String),
    });
  });

  it('should handle unknown exception and return default structure', () => {
    filter.catch('random string' as any, mockArgumentsHost);
    expect(statusCode).toBe(HttpStatus.BAD_REQUEST);
    expect(jsonResponse).toMatchObject({
      statusCode: HttpStatus.BAD_REQUEST,
      message: 'random string',
      error: 'UnknownException',
      path: '/test-url',
      timestamp: expect.any(String),
    });
  });

  it('should include stack trace in development mode', () => {
    process.env.NODE_ENV = 'development';
    const exception = new Error('Stack error');
    filter.catch(exception, mockArgumentsHost);
    expect(jsonResponse.stack).toBeDefined();
    process.env.NODE_ENV = '';
  });

  it('should not include stack trace in production mode', () => {
    process.env.NODE_ENV = 'production';
    const exception = new Error('No stack in prod');
    filter.catch(exception, mockArgumentsHost);
    expect(jsonResponse.stack).toBeUndefined();
    process.env.NODE_ENV = '';
  });
}); 