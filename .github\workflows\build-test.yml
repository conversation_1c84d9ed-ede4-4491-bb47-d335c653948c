name: Build and Test

on:
  workflow_call:

jobs:
  build-test:
    name: Build and Test
    runs-on: [self-hosted, linux]
    services:
      docker:
        image: docker:24.0-dind
        options: --privileged --volume /var/run/docker.sock:/var/run/docker.sock
    env:
      DOCKER_HOST: unix:///var/run/docker.sock
    steps:
      - uses: actions/checkout@v3

      - name: Set up Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '20'

      - name: Install dependencies
        run: npm ci

      - name: Build project
        run: npm run build

      - name: Run Unit & Integration Tests with Coverage
        run: npm run test:coverage
        env:
          NODE_ENV: test

      - name: Upload Coverage Report
        uses: actions/upload-artifact@v4
        with:
          name: coverage-report
          path: coverage/lcov.info

      - name: Upload Test Results (if using jest-junit)
        if: always()
        uses: actions/upload-artifact@v4
        with:
          name: junit-test-report
          path: junit.xml
          if-no-files-found: ignore
