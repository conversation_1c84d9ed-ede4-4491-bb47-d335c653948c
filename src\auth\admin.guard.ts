import { Injectable, CanActivate, ExecutionContext, ForbiddenException, Inject } from '@nestjs/common';
import { Knex } from 'knex';

@Injectable()
export class AdminGuard implements CanActivate {
  constructor(@Inject('KNEX_CONNECTION') private readonly knex: Knex) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest();
    const user = request.user;

    if (!user?.userId) {
      throw new ForbiddenException('User not authenticated');
    }

    // Fetch user from database to check role
    const dbUser = await this.knex('users')
      .where({ id: user.userId })
      .first();

    if (!dbUser) {
      throw new ForbiddenException('User not found');
    }

    if (dbUser.role !== 'ADMIN') {
      throw new ForbiddenException('Admin access required');
    }

    return true;
  }
}
