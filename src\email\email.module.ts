import { Module } from '@nestjs/common';
import { MailerModule } from '@nestjs-modules/mailer';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { EmailService } from './email.service';
import { SendGridService } from './sendgrid.service';

@Module({
  imports: [
    ConfigModule,
    MailerModule.forRootAsync({
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: async (configService: ConfigService) => {
        const host = configService.get('email.host');
        const port = configService.get('email.port');
        const isGmail = host === 'smtp.gmail.com';
        
        return {
          transport: {
            host,
            port,
            secure: false, // true for 465, false for other ports
            auth: {
              user: configService.get('email.user'),
              pass: configService.get('email.pass'),
            },
            // Gmail-specific settings
            ...(isGmail && {
              connectionTimeout: 10000, // 10 seconds
              greetingTimeout: 10000, // 10 seconds
              socketTimeout: 10000, // 10 seconds
            }),
          },
          defaults: {
            from: configService.get('email.from'),
          },
        };
      },
    }),
  ],
  providers: [EmailService, SendGridService],
  exports: [EmailService, SendGridService],
})
export class EmailModule {} 