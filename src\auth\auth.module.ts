import { Module } from '@nestjs/common';
import { JwtModule } from '@nestjs/jwt';
import { PassportModule } from '@nestjs/passport';
import { AuthService } from './auth.service';
import { AuthController, OAuth2Controller } from './auth.controller';
import { AdminController } from './admin.controller';
import { JwtStrategy } from './jwt.strategy';
import { GoogleStrategy } from './google.strategy';
import { AuthGuard } from './auth.guard';
import { AdminGuard } from './admin.guard';
import { UsersModule } from '../users/users.module';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { EmailModule } from '../email/email.module';
import { LoggerModule } from '../common/logger/logger.module';
import googleOAuthConfig from '../config/google-oauth.config';

@Module({
  imports: [
    ConfigModule.forFeature(googleOAuthConfig),
    UsersModule,
    PassportModule,
    JwtModule.registerAsync({
      imports: [ConfigModule],
      useFactory: async (configService: ConfigService) => ({
        secret: configService.get('JWT_SECRET'),
        signOptions: { expiresIn: configService.get('JWT_EXPIRATION') },
      }),
      inject: [ConfigService],
    }),
    EmailModule,
    LoggerModule,
  ],
  controllers: [AuthController, OAuth2Controller, AdminController],
  providers: [AuthService, JwtStrategy, GoogleStrategy, AuthGuard, AdminGuard],
  exports: [AuthGuard, AdminGuard],
})
export class AuthModule {} 