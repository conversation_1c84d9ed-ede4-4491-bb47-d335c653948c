import { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  await knex.schema.alterTable('users', (table) => {
    table.string('role', 50).notNullable().defaultTo('USER');
    table.index(['role'], 'idx_users_role');
  });
}

export async function down(knex: Knex): Promise<void> {
  await knex.schema.alterTable('users', (table) => {
    table.dropIndex(['role'], 'idx_users_role');
    table.dropColumn('role');
  });
}
