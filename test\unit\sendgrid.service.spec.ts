// WARNING: All infrastructure dependencies (DB, email, external services) must be mocked using jest.fn(). No real calls allowed in unit tests.
import { Test, TestingModule } from '@nestjs/testing';
import { ConfigService } from '@nestjs/config';
import { ServiceUnavailableException } from '@nestjs/common';
import { SendGridService } from '../../src/email/sendgrid.service';

describe('SendGridService', () => {
  let service: SendGridService;
  let configService: jest.Mocked<ConfigService>;
  let consoleSpy: jest.SpyInstance;
  let consoleErrorSpy: jest.SpyInstance;

  beforeEach(async () => {
    // Reset all mocks
    jest.clearAllMocks();
    
    // Mock console methods
    consoleSpy = jest.spyOn(console, 'log').mockImplementation();
    consoleErrorSpy = jest.spyOn(console, 'error').mockImplementation();

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        SendGridService,
        {
          provide: ConfigService,
          useValue: {
            get: jest.fn(),
          },
        },
      ],
    }).compile();

    service = module.get<SendGridService>(SendGridService);
    configService = module.get(ConfigService);
  });

  afterEach(() => {
    consoleSpy?.mockRestore();
    consoleErrorSpy?.mockRestore();
    jest.clearAllMocks();
  });

  describe('initialization', () => {
    it('should not initialize when API key is not configured', async () => {
      configService.get.mockReturnValue(undefined);

      // Create a new service instance to test initialization
      const module: TestingModule = await Test.createTestingModule({
        providers: [
          SendGridService,
          {
            provide: ConfigService,
            useValue: configService,
          },
        ],
      }).compile();

      const newService = module.get<SendGridService>(SendGridService);

      expect(newService.isAvailable()).toBe(false);
      expect(consoleSpy).toHaveBeenCalledWith('[SENDGRID] API key not configured - SendGrid service not available');
    });

    it('should initialize successfully when API key is configured and package is available', async () => {
      configService.get.mockReturnValue('test-api-key');

      // Since @sendgrid/mail is installed, the service should initialize successfully
      const module: TestingModule = await Test.createTestingModule({
        providers: [
          SendGridService,
          {
            provide: ConfigService,
            useValue: configService,
          },
        ],
      }).compile();

      const newService = module.get<SendGridService>(SendGridService);

      expect(newService.isAvailable()).toBe(true);
      expect(consoleSpy).toHaveBeenCalledWith('[SENDGRID] Initialized successfully');
    });
  });

  describe('sendEmail', () => {
    it('should throw ServiceUnavailableException when not initialized', async () => {
      // Create service that is not initialized (no API key)
      configService.get.mockReturnValue(undefined);

      const module: TestingModule = await Test.createTestingModule({
        providers: [
          SendGridService,
          {
            provide: ConfigService,
            useValue: configService,
          },
        ],
      }).compile();

      const uninitializedService = module.get<SendGridService>(SendGridService);

      await expect(uninitializedService.sendEmail('<EMAIL>', 'Test Subject', '<p>Test Content</p>'))
        .rejects.toThrow(ServiceUnavailableException);
      await expect(uninitializedService.sendEmail('<EMAIL>', 'Test Subject', '<p>Test Content</p>'))
        .rejects.toThrow('SendGrid is not properly configured or package not installed');
    });

    it('should handle SendGrid API errors gracefully', async () => {
      // Since @sendgrid/mail is installed, test error handling
      configService.get.mockImplementation((key: string) => {
        if (key === 'email.sendgridApiKey') return 'test-api-key';
        if (key === 'email.sendgridFrom') return '<EMAIL>';
        return undefined;
      });

      const module: TestingModule = await Test.createTestingModule({
        providers: [
          SendGridService,
          {
            provide: ConfigService,
            useValue: configService,
          },
        ],
      }).compile();

      const serviceWithApiKey = module.get<SendGridService>(SendGridService);

      // Should be available since package is installed and API key is configured
      expect(serviceWithApiKey.isAvailable()).toBe(true);

      // Mock the SendGrid send method to throw an error
      const sgMail = require('@sendgrid/mail');
      const originalSend = sgMail.send;
      sgMail.send = jest.fn().mockRejectedValue(new Error('SendGrid API error'));

      await expect(serviceWithApiKey.sendEmail('<EMAIL>', 'Test Subject', '<p>Test Content</p>'))
        .rejects.toThrow('Failed to send email via SendGrid');

      // Restore original method
      sgMail.send = originalSend;
    });
  });

  describe('isAvailable', () => {
    it('should return false when not initialized', async () => {
      configService.get.mockReturnValue(undefined);

      // Create service that is not initialized
      const module: TestingModule = await Test.createTestingModule({
        providers: [
          SendGridService,
          {
            provide: ConfigService,
            useValue: configService,
          },
        ],
      }).compile();

      const uninitializedService = module.get<SendGridService>(SendGridService);
      expect(uninitializedService.isAvailable()).toBe(false);
    });

    it('should return true when package is installed and API key is configured', async () => {
      configService.get.mockReturnValue('test-api-key');

      // With API key and package installed, service should be available
      const module: TestingModule = await Test.createTestingModule({
        providers: [
          SendGridService,
          {
            provide: ConfigService,
            useValue: configService,
          },
        ],
      }).compile();

      const serviceWithApiKey = module.get<SendGridService>(SendGridService);
      expect(serviceWithApiKey.isAvailable()).toBe(true);
    });
  });
});
