version: '3.3'
services:
  postgres:
    image: postgres:13
    container_name: userauth-postgres
    ports:
      - '5433:5432'
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password
      POSTGRES_DB: userauth
    volumes:
      - userauth_pgdata:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 10s
      timeout: 5s
      retries: 5
    restart: on-failure
    networks:
      - auth-network
    security_opt:
      - no-new-privileges:true
  sonarqube:
    image: sonarqube:community
    container_name: sonarqube
    ports:
      - "9002:9000"
    environment:
      - SONAR_ES_BOOTSTRAP_CHECKS_DISABLE=true
    volumes:
      - sonarqube_data:/opt/sonarqube/data
      - sonarqube_logs:/opt/sonarqube/logs
      - sonarqube_extensions:/opt/sonarqube/extensions
    security_opt:
      - no-new-privileges:true
    read_only: true
volumes:
  userauth_pgdata:
    driver: local
  sonarqube_data:
  sonarqube_logs:
  sonarqube_extensions:
networks:
  auth-network:
    driver: bridge