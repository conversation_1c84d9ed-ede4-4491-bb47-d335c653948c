# Dependencies
node_modules
npm-debug.log*

# Build outputs
!dist
build

# Coverage and test reports
coverage
.nyc_output
test-results

# Environment files
.env
.env.local
.env.development
.env.test
.env.production

# Logs
logs
*.log

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# IDE and editor files
.vscode
.idea
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Git
.git
.gitignore

# Docker
Dockerfile
.dockerignore
docker-compose.yml

# CI/CD
.github

# Documentation
README.md
docs

# Kubernetes
k8s

# SonarQube
.scannerwork
sonar-project.properties

# Temporary files
tmp
temp
