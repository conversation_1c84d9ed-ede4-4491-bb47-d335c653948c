name: GitOps Deployment

on:
  workflow_call:

jobs:
  cd-gitops-deploy:
    name: Trigger GitOps Deployment (NestJS Backend)
    runs-on: [self-hosted, linux]
    # if: github.event_name == 'push' && github.ref_name == 'main'
    steps:
      - name: Encode secrets as base64
        id: secrets
        run: |
          SECRETS_JSON=$(cat << EOF | base64 -w 0
          {
            "JWT_SECRET": "${{ secrets.JWT_SECRET }}",
            "DB_USER": "${{ secrets.DB_USER }}",
            "DB_PASSWORD": "${{ secrets.DB_PASSWORD }}",
            "DB_HOST": "${{ secrets.DB_HOST }}",
            "DB_PORT": "${{ secrets.DB_PORT }}",
            "DB_NAME": "${{ secrets.DB_NAME }}",
            # "SMTP_HOST": "smtp.gmail.com",
            # "SMTP_PORT": "587",
            "SMTP_USER": "${{ secrets.SMTP_USER }}",
            "SMTP_PASS": "${{ secrets.SMTP_PASS }}",
            # "SMTP_FROM": "${{ secrets.SMTP_FROM }}",
            "GOOGLE_CLIENT_ID": "${{ secrets.GOOGLE_CLIENT_ID }}",
            "GOOGLE_CLIENT_SECRET": "${{ secrets.GOOGLE_CLIENT_SECRET }}"
          }
          EOF
          )
          echo "secrets_encoded=$SECRETS_JSON" >> $GITHUB_OUTPUT
          
      - name: 🚀 Trigger GitOps deployment for Nest Boot backend
        uses: actions/github-script@v7
        env:
          SECRETS_ENCODED: ${{ steps.secrets.outputs.secrets_encoded }}
        with:
          github-token: ${{ secrets.GITOPS_TOKEN }}
          script: |
            console.log('🚀 Triggering GitOps deployment for Nest Boot backend...');
            console.log('Branch:', '${{ github.ref_name }}');
            console.log('Event:', '${{ github.event_name }}');
            
            const secretsEncoded = process.env.SECRETS_ENCODED || '';
            const dockerTag = 'latest'; // or set dynamically if you use tags
            let environment = 'dev';
            
            const payload = {
              app_name: 'ai-nest-backend',
              project_id: 'ai-nest-backend',
              application_type: 'nest-backend',
              environment: environment,
              docker_image: 'registry.digitalocean.com/doks-registry/ai-nest-backend',
              docker_tag: dockerTag,
              source_repo: `${context.repo.owner}/${context.repo.repo}`,
              source_branch: '${{ github.ref_name }}',
              commit_sha: context.sha,
              secrets_encoded: secretsEncoded || ''
            };
            
            console.log('📦 Dispatch payload:', JSON.stringify(payload, null, 2));
            console.log('🔍 Payload details:');
            console.log('   - App Name:', payload.app_name);
            console.log('   - Environment:', payload.environment);
            console.log('   - Docker Image:', payload.docker_image);
            console.log('   - Source Branch:', payload.source_branch);
            console.log('   - Secrets Encoded Length:', payload.secrets_encoded?.length || 0);
            
            const requiredFields = ['app_name', 'project_id', 'environment', 'docker_image', 'docker_tag'];
            for (const field of requiredFields) {
              if (!payload[field] || payload[field] === '') {
                throw new Error(`Required field '${field}' is missing or empty`);
              }
            }
            
            if (!/^[a-z0-9-]+$/.test(payload.project_id)) {
              throw new Error(`Invalid project_id format: ${payload.project_id}. Must be lowercase alphanumeric with hyphens only.`);
            }
            
            if (!['dev', 'staging', 'prod', 'production'].includes(payload.environment)) {
              throw new Error(`Invalid environment: ${payload.environment}. Must be dev, staging, prod, or production.`);
            }
            
            try {
              await github.rest.repos.createDispatchEvent({
                owner: 'ChidhagniConsulting',
                repo: 'gitops-argocd-apps',
                event_type: 'deploy-to-argocd',
                client_payload: payload
              });
              
              console.log(`✅ GitOps deployment triggered successfully!`);
              console.log(`📱 App: ai-nest-backend`);
              console.log(`🌍 Environment: ${environment}`);
              console.log(`🐳 Docker image: registry.digitalocean.com/doks-registry/ai-nest-backend:${dockerTag}`);
              console.log(`🌿 Source branch: ${'${{ github.ref_name }}'}`);
              console.log(`📝 Commit SHA: ${context.sha}`);
              console.log(`🔗 Monitor deployment: https://github.com/ChidhagniConsulting/gitops-argocd-apps/actions`);
            } catch (error) {
              console.error('❌ Failed to trigger GitOps deployment:', error);
              console.error('Error details:', error.message);
              if (error.response) {
                console.error('Response status:', error.response.status);
                console.error('Response data:', error.response.data);
              }
              throw error;
            }