import { IsEmail, IsString, IsBoolean, Length } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class LoginDto {
  @ApiProperty({ 
    description: 'User email address',
    example: '<EMAIL>'
  })
  @IsEmail()
  email!: string;

  @ApiProperty({ 
    description: 'User password',
    example: 'password123'
  })
  @IsString()
  @Length(1)
  password!: string;

  @ApiProperty({ 
    description: 'User IP address',
    example: '*********'
  })
  @IsString()
  @Length(1)
  ipAddress!: string;

  @ApiProperty({ 
    description: 'Device details',
    example: 'Chrome 120.0.0.0 on Windows 10'
  })
  @IsString()
  @Length(1)
  deviceDetails!: string;

  @ApiProperty({ 
    description: 'Override existing logins',
    example: false
  })
  @IsBoolean()
  overrideExistingLogins!: boolean;
} 