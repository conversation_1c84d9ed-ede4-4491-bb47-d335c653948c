// WARNING: All infrastructure dependencies (DB, email, external services) must be mocked using jest.fn(). No real calls allowed in unit tests.
import { Test, TestingModule } from '@nestjs/testing';
import { AuthService } from '../../src/auth/auth.service';
import { JwtService } from '@nestjs/jwt';
import { ConfigService } from '@nestjs/config';
import { EmailService } from '../../src/email/email.service';
import { Knex } from 'knex';
import * as bcrypt from 'bcryptjs';
import { BadRequestException, UnauthorizedException } from '@nestjs/common';
import { LoggerService } from '../../src/common/logger/logger.service';

jest.mock('bcryptjs');
jest.mock('uuid', () => ({ v4: () => 'mock-uuid' }));

describe('AuthService', () => {
  let service: AuthService;
  let emailService: jest.Mocked<EmailService>;
  let queryBuilder: any;
  let knexMock: any;
  let trx: any;

  beforeEach(async () => {
    queryBuilder = {
      where: jest.fn().mockReturnThis(),
      first: jest.fn(),
      insert: jest.fn().mockReturnThis(),
      update: jest.fn().mockReturnThis(),
      returning: jest.fn().mockResolvedValue([{ id: '1' }]),
    };

    trx = {
        ...queryBuilder,
        commit: jest.fn(),
        rollback: jest.fn(),
    };
    
    knexMock = jest.fn(() => queryBuilder);
    knexMock.transaction = jest.fn(async (callback) => callback(trx));


    const module: TestingModule = await Test.createTestingModule({
      providers: [
        AuthService,
        { provide: JwtService, useValue: { sign: jest.fn().mockReturnValue('jwt-token') } },
        { provide: ConfigService, useValue: { get: jest.fn((key: string) => key) } },
        { provide: EmailService, useValue: { sendVerificationLink: jest.fn(), sendPasswordResetLink: jest.fn() } },
        { provide: 'KNEX_CONNECTION', useValue: knexMock },
        { provide: LoggerService, useValue: { log: jest.fn(), error: jest.fn(), warn: jest.fn(), debug: jest.fn(), verbose: jest.fn(), setContext: jest.fn() } },
      ],
    }).compile();

    service = module.get<AuthService>(AuthService);
    emailService = module.get(EmailService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('register', () => {
    it.each([
      [
        { email: 'invalid-email', name: 'Test', mobileNumber: '123' },
        'Invalid email address',
      ],
      [
        { email: '', name: 'Test', mobileNumber: '123' },
        'Missing required fields',
      ],
    ])('should throw for invalid input: %p', async (input, expectedError) => {
      await expect(service.register(input)).rejects.toThrow(expectedError);
    });

    it('should throw BadRequestException if mobile number is already in use', async () => {
      queryBuilder.first.mockResolvedValueOnce(null).mockResolvedValueOnce({ mobile_number: '123' });
      await expect(service.register({ email: '<EMAIL>', name: 'Test', mobileNumber: '123' })).rejects.toThrow('Mobile number already in use');
    });

    it('should throw BadRequestException if user is already verified', async () => {
      queryBuilder.first.mockResolvedValue({ verified: true });
      await expect(service.register({ email: '<EMAIL>', name: 'Test', mobileNumber: '123' })).rejects.toThrow(BadRequestException);
    });

    it('should send a verification link for a new user', async () => {
      queryBuilder.first.mockResolvedValue(undefined);
      await service.register({ email: '<EMAIL>', name: 'Test', mobileNumber: '123' });
      expect(emailService.sendVerificationLink).toHaveBeenCalled();
    });

    it('should update the verification token for an existing, unverified user', async () => {
      queryBuilder.first.mockResolvedValueOnce({ verified: false }).mockResolvedValueOnce(null);
      await service.register({ email: '<EMAIL>', name: 'Test', mobileNumber: '123' });
      expect(queryBuilder.update).toHaveBeenCalled();
      expect(emailService.sendVerificationLink).toHaveBeenCalled();
    });
  });

  describe('verifyEmail', () => {
    it.each([
      [null, 'Invalid or expired token.'],
      [{ expires_at: new Date(Date.now() - 1000) }, 'Token expired.'],
    ])('should throw for invalid or expired token: %p', async (mockValue, expectedError) => {
      queryBuilder.first.mockResolvedValue(mockValue);
      await expect(service.verifyEmail('some-token')).rejects.toThrow(expectedError);
    });

    it('should verify the email', async () => {
      const verificationData = {
        id: 'verification-id',
        contact_value: '<EMAIL>',
        name: 'Test User',
        mobile_number: '1234567890',
        expires_at: new Date(Date.now() + 3600 * 1000),
        verified: false,
      };
      queryBuilder.first.mockResolvedValue(verificationData);
      queryBuilder.update.mockResolvedValue(1);

      await service.verifyEmail('valid-token');

      expect(queryBuilder.update).toHaveBeenCalledWith({ verified: true, verified_at: expect.any(Date) });
    });
  });

  describe('login', () => {
    it.each([
      [undefined, UnauthorizedException],
      [{ id: '1', password: 'hashed_password' }, 'Invalid credentials.'],
    ])('should throw for invalid login: %p', async (mockUser, expectedError) => {
      queryBuilder.first.mockResolvedValue(mockUser);
      if (mockUser) {
        (bcrypt.compare as jest.Mock).mockResolvedValue(false);
      }
      await expect(service.login({email: 'a', password: 'b', ipAddress: 'c', deviceDetails: 'd', overrideExistingLogins: false})).rejects.toThrow(expectedError);
    });

    it('should return tokens for a valid login', async () => {
      const user = { id: '1', password: 'hashed_password', email: 'a', first_name: 'Test', last_name: 'User' };
      queryBuilder.first.mockResolvedValue(user);
      (bcrypt.compare as jest.Mock).mockResolvedValue(true);
      const result = await service.login({email: 'a', password: 'b', ipAddress: 'c', deviceDetails: 'd', overrideExistingLogins: false});
      expect(result).toHaveProperty('sessionToken');
      expect(result).not.toHaveProperty('user');
    });

    it('should override existing sessions if requested', async () => {
        queryBuilder.first.mockResolvedValue({ id: '1', password: 'hashed_password' });
        (bcrypt.compare as jest.Mock).mockResolvedValue(true);
        await service.login({email: 'a', password: 'b', ipAddress: 'c', deviceDetails: 'd', overrideExistingLogins: true});
        expect(queryBuilder.update).toHaveBeenCalledWith({ is_active: false, logged_out_at: expect.any(Date) });
    });
  });

  describe('googleAuth', () => {
    const googleUser = { email: '<EMAIL>', googleId: '123', firstName: 'Test', lastName: 'User', picture: 'pic.jpg'};
    const authDetails = { ipAddress: 'ip', deviceDetails: 'device' };

    it('should throw error for invalid google user data', async () => {
        await expect(service.googleAuth({ ...googleUser, email: null }, 'ip', 'device')).rejects.toThrow('Invalid Google user data: missing email or Google ID');
    });

    it('should create a new user if not found by googleId or email', async () => {
        queryBuilder.first
            .mockResolvedValueOnce(null) 
            .mockResolvedValueOnce(null) 
            .mockResolvedValueOnce({ id: 'new-user-id', ...googleUser, first_name: 'Test', last_name: 'User' }) 
            .mockResolvedValueOnce(null);

        await service.googleAuth(googleUser, authDetails.ipAddress, authDetails.deviceDetails);
        
        expect(queryBuilder.insert).toHaveBeenCalledTimes(3);
    });

    it('should link googleId to an existing user by email', async () => {
        const existingUser = { id: 'existing-user-id', email: googleUser.email };
        queryBuilder.first
            .mockResolvedValueOnce(null)
            .mockResolvedValueOnce(existingUser);

        await service.googleAuth(googleUser, authDetails.ipAddress, authDetails.deviceDetails);

        expect(queryBuilder.update).toHaveBeenCalledWith(expect.objectContaining({ google_id: googleUser.googleId }));
    });

    it('should use existing user by googleId', async () => {
        const existingUser = { id: 'existing-user-id', email: googleUser.email, google_id: googleUser.googleId };
        queryBuilder.first.mockResolvedValueOnce(existingUser);

        await service.googleAuth(googleUser, authDetails.ipAddress, authDetails.deviceDetails);
        expect(knexMock).toHaveBeenCalledWith('users');
    });
  });

  describe('resendVerification', () => {
    it.each([
      [null, 'No unverified record found for this email.'],
      [{ verified: true }, 'Email already verified.'],
    ])('should throw for invalid resend verification: %p', async (mockValue, expectedError) => {
      queryBuilder.first.mockResolvedValue(mockValue);
      await expect(service.resendVerification({ email: 'a' })).rejects.toThrow(expectedError);
    });

    it('should resend the verification email', async () => {
      queryBuilder.first
        .mockResolvedValueOnce(null) // First check for verified user -> not found
        .mockResolvedValueOnce({ id: '1', contact_value: '<EMAIL>', name: 'Test', mobile_number: '123' }); // Second check for unverified user -> found
      await service.resendVerification({ email: '<EMAIL>' });
      expect(emailService.sendVerificationLink).toHaveBeenCalled();
    });
  });

  describe('forgotPassword', () => {
    it.each([
      [null, 'User not found.'],
    ])('should throw for invalid forgot password: %p', async (mockValue, expectedError) => {
      queryBuilder.first.mockResolvedValue(mockValue);
      await expect(service.forgotPassword({ email: 'a', ipAddress: 'ip', deviceDetails: 'device' })).rejects.toThrow(expectedError);
    });

    it('should send a password reset link', async () => {
      queryBuilder.first.mockResolvedValue({ id: '1', email: 'a' });
      await service.forgotPassword({ email: 'a', ipAddress: 'ip', deviceDetails: 'device' });
      expect(queryBuilder.insert).toHaveBeenCalled();
      expect(emailService.sendPasswordResetLink).toHaveBeenCalled();
    });
  });

  describe('resetPassword', () => {
    it.each([
      [null, 'Invalid or expired reset token.'],
      [{ expires_at: new Date(Date.now() - 1000) }, 'Reset token expired.'],
    ])('should throw for invalid or expired reset token: %p', async (mockValue, expectedError) => {
      queryBuilder.first.mockResolvedValue(mockValue);
      await expect(service.resetPassword({ resetToken: 't', newPassword: 'p', ipAddress: 'ip', deviceDetails: 'device' })).rejects.toThrow(expectedError);
    });

    it('should throw an error if the new password is the same as the old one', async () => {
      queryBuilder.first
        .mockResolvedValueOnce({ user_id: '1', expires_at: new Date(Date.now() + 1000) })
        .mockResolvedValueOnce({ id: '1', password: 'old_hashed_password' });
      (bcrypt.compare as jest.Mock).mockResolvedValue(true);
      await expect(service.resetPassword({ resetToken: 't', newPassword: 'p', ipAddress: 'ip', deviceDetails: 'device' })).rejects.toThrow('New password cannot be the same as the old password.');
    });

    it('should reset the password successfully', async () => {
      queryBuilder.first
        .mockResolvedValueOnce({ user_id: '1', expires_at: new Date(Date.now() + 1000) })
        .mockResolvedValueOnce({ id: '1', password: 'old_hashed_password' });
      (bcrypt.compare as jest.Mock).mockResolvedValue(false);
      (bcrypt.hash as jest.Mock).mockResolvedValue('new_hashed_password');
      await service.resetPassword({ resetToken: 't', newPassword: 'p', ipAddress: 'ip', deviceDetails: 'device' });
      expect(queryBuilder.update).toHaveBeenCalledWith(expect.objectContaining({ password: 'new_hashed_password' }));
    });
  });

  describe('logout', () => {
    it('should deactivate the user session', async () => {
        queryBuilder.first.mockResolvedValue({ session_token: 'a' });
        await service.logout({ sessionToken: 'a', ipAddress: 'ip', deviceDetails: 'device' }, { id: '1' });
        expect(queryBuilder.update).toHaveBeenCalledWith({ is_active: false, logged_out_at: expect.any(Date), ip_address: 'ip', device_details: 'device' });
    });
  });

  describe('createUser', () => {
    const createUserDto = {
      email: '<EMAIL>',
      password: 'password123',
      ipAddress: '127.0.0.1',
      deviceDetails: 'test-device',
    };
    const verificationData = {
      contact_value: createUserDto.email,
      verified: true,
      name: 'Test User',
      mobile_number: '1234567890',
    };
  
    it('should throw an error if email is not verified', async () => {
      queryBuilder.first.mockResolvedValue(null);
      await expect(service.createUser(createUserDto)).rejects.toThrow('Email not verified.');
    });
  
    it('should throw an error if user already exists', async () => {
      queryBuilder.first
        .mockResolvedValueOnce(verificationData)
        .mockResolvedValueOnce({ id: '1' });
      await expect(service.createUser(createUserDto)).rejects.toThrow('User already exists.');
    });
  
    it('should create a new user and return tokens', async () => {
        trx.returning.mockResolvedValue([{ id: 'new-user-id' }]);
        queryBuilder.first
            .mockResolvedValueOnce(verificationData)
            .mockResolvedValueOnce(null);
        (bcrypt.hash as jest.Mock).mockResolvedValue('hashed_password');

        const result = await service.createUser(createUserDto);

        expect(trx.insert).toHaveBeenCalled();
        expect(result).toHaveProperty('sessionToken');
        expect(result).toHaveProperty('userId', 'new-user-id');
    });
  
    it('should throw an error if user creation fails', async () => {
        queryBuilder.first
            .mockResolvedValueOnce(verificationData)
            .mockResolvedValueOnce(null);
        (bcrypt.hash as jest.Mock).mockResolvedValue('hashed_password');
        
        trx.insert.mockImplementation(() => {
            return {
                returning: jest.fn().mockRejectedValue(new Error('DB error'))
            };
        });

        await expect(service.createUser(createUserDto)).rejects.toThrow('DB error');
    });
  });
});