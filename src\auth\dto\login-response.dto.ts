import { ApiProperty } from '@nestjs/swagger';

export class LoginResponseDto {
  @ApiProperty({ 
    description: 'User ID',
    example: '9418e651-19a5-42f5-9e64-50db85275851'
  })
  userId!: string;

  @ApiProperty({ 
    description: 'Session token for session management',
    example: 'd3256c2e-75f2-4307-a9a9-8dbb4ee55998792d8d33-3980-48f3-bc4e-26e8f842b3d9'
  })
  sessionToken!: string;

  @ApiProperty({ 
    description: 'Refresh token for token renewal',
    example: '881800b8-7cd3-46bc-a1ff-1391e64fd26bb29b664a-356c-45e9-87e3-324b622c529f'
  })
  refreshToken!: string;

  @ApiProperty({ 
    description: 'JWT access token for API authentication',
    example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...'
  })
  accessToken!: string;

  @ApiProperty({ 
    description: 'Session expiration timestamp',
    example: '2025-07-26T10:03:07.759Z'
  })
  expiresAt!: string;

  @ApiProperty({ 
    description: 'Refresh token expiration timestamp',
    example: '2025-08-24T10:03:07.759Z'
  })
  refreshExpiresAt!: string;
}
