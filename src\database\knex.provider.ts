import { Provider } from '@nestjs/common';
import Knex from 'knex';
import { ConfigService } from '@nestjs/config';

export const KnexProvider: Provider = {
  provide: 'KNEX_CONNECTION',
  inject: [ConfigService],
  useFactory: async (configService: ConfigService) => {
    const knex = Knex({
      client: 'pg',
      connection: configService.get('database'),
      pool: { min: 2, max: 10 },
    });
    
    await knex.raw('select 1+1 as result');
    return knex;
  },
}; 