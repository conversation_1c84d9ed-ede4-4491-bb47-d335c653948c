import { PostgreSqlContainer, StartedPostgreSqlContainer } from '@testcontainers/postgresql';
import knex, { Knex } from 'knex';

let container: StartedPostgreSqlContainer;
let db: Knex;

export async function startTestDb() {
  // Ensure test environment is set
  process.env.NODE_ENV = 'test';
  
  // Set dummy SMTP credentials for testing
  process.env.SMTP_HOST = 'smtp.test.com';
  process.env.SMTP_PORT = '587';
  process.env.SMTP_USER = '<EMAIL>';
  process.env.SMTP_PASS = 'testpassword';
  process.env.SMTP_FROM = '<EMAIL>';
  
  // Set other test environment variables
  process.env.APP_URL = 'http://localhost:3000';
  process.env.JWT_SECRET = 'test-jwt-secret-key-for-testing-only';
  process.env.JWT_EXPIRES_IN = '1h';
  process.env.GOOGLE_CLIENT_ID = 'test-client-id';
  process.env.GOOGLE_CLIENT_SECRET = 'test-client-secret';
  process.env.GOOGLE_CALLBACK_URL = 'http://localhost:8080/oauth2/callback/google';
  process.env.CORS_ORIGIN = 'http://localhost:3000';

  container = await new PostgreSqlContainer('postgres:15')
    .withDatabase('testdb')
    .withUsername('testuser')
    .withPassword('testpass')
    .start();

  const connectionString = container.getConnectionUri();
  process.env.DATABASE_URL = connectionString;

  db = knex({
    client: 'pg',
    connection: connectionString,
    migrations: {
      directory: 'src/database/migrations',
      extension: 'ts',
    },
    seeds: {
      directory: 'seeds',
      extension: 'ts',
    },
  });

  await db.migrate.latest();
  await db.seed.run();
}

export async function stopTestDb() {
  if (db) await db.destroy();
  if (container) await container.stop();
}

export { container, db }; 