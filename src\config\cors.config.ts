import { registerAs } from '@nestjs/config';

export const corsConfig = registerAs('cors', () => {
  // Clean up the origins string by removing extra spaces and line breaks
  const allowedOrigins = process.env.CORS_ALLOWED_ORIGINS
    ? process.env.CORS_ALLOWED_ORIGINS
        .split(',')
        .map(origin => origin.trim())
        .filter(origin => origin.length > 0)
    : ['http://localhost:3000', 'http://localhost:3001'];

  // Clean up the methods string
  const allowedMethods = process.env.CORS_ALLOWED_METHODS
    ? process.env.CORS_ALLOWED_METHODS
        .split(',')
        .map(method => method.trim())
        .filter(method => method.length > 0)
    : ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'];

  return {
    allowedOrigins,
    allowedMethods,
    allowCredentials: process.env.CORS_ALLOW_CREDENTIALS ? process.env.CORS_ALLOW_CREDENTIALS === 'true' : true,
    maxAge: parseInt(process.env.CORS_MAX_AGE ?? '3600', 10),
  };
}); 