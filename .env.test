# .env.test
# This file contains environment variables exclusively for the test environment.
# It is loaded by the Jest setup file before any tests are run.
# JWT
JWT_SECRET=supersecretkeyfortesting
SESSION_SECRET=test-session-secret
# Google OAuth - Dummy values for testing
GOOGLE_CLIENT_ID=dummy-google-client-id
GOOGLE_CLIENT_SECRET=dummy-google-client-secret
GOOGLE_REDIRECT_URI=http://localhost:8080/oauth2/callback/google
# Test Database
# These should match the credentials in your test database setup (e.g., docker-compose.yml for tests)
DB_HOST=localhost
DB_PORT=5433
DB_USER=postgres
DB_PASSWORD=password
DB_NAME=userauth
#Email SMTP configuration
SMTP_HOST=smtp.test.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=testpassword
SMTP_FROM='"No Reply" <EMAIL>'
# Node environment
NODE_ENV=test