import { Injectable, BadRequestException, ServiceUnavailableException } from '@nestjs/common';
import { MailerService } from '@nestjs-modules/mailer';
import { ConfigService } from '@nestjs/config';
import { SendGridService } from './sendgrid.service';

interface EmailProvider {
  name: string;
  host?: string;
  port?: number;
  user?: string;
  pass?: string;
  from: string;
  dailyLimit: number;
  currentUsage: number;
  type: 'smtp' | 'sendgrid';
}

@Injectable()
export class EmailService {
  private emailProvider: EmailProvider | null = null;

  constructor(
    private readonly mailerService: MailerService,
    private readonly configService: ConfigService,
    private readonly sendGridService: SendGridService,
  ) {
    this.initializeEmailProvider();
  }

  private initializeEmailProvider(): void {
    // In test mode, do not set an email provider; just log emails as [MOCK EMAIL]
    if (process.env.NODE_ENV === 'test') {
      console.log(`[EMAIL] Test environment detected: emails will be logged as [MOCK EMAIL] and not sent.`);
      return;
    }

    const providerType = this.configService.get<string>('email.provider') || 'smtp';
    
    if (providerType === 'sendgrid' && this.sendGridService.isAvailable()) {
      this.initializeSendGrid();
    } else if (providerType === 'smtp') {
      this.initializeSMTP();
    } else {
      console.log(`[EMAIL] No email provider configured - emails will be logged instead`);
    }
  }

  private initializeSendGrid(): void {
    const sendgridFrom = this.configService.get<string>('email.sendgridFrom');

    this.emailProvider = {
      name: 'SendGrid',
      from: sendgridFrom || '<EMAIL>',
      dailyLimit: 100000, // SendGrid free tier limit
      currentUsage: 0,
      type: 'sendgrid',
    };
    
    console.log(`[EMAIL] Initialized SendGrid provider`);
  }

  private initializeSMTP(): void {
    const smtpHost = this.configService.get<string>('email.host');
    const smtpUser = this.configService.get<string>('email.user');
    const smtpPass = this.configService.get<string>('email.pass');
    const smtpFrom = this.configService.get<string>('email.from');

    // Only initialize if SMTP is properly configured
    if (smtpHost && smtpUser && smtpPass) {
      this.emailProvider = {
        name: 'Gmail',
        host: smtpHost,
        port: parseInt(this.configService.get<string>('email.port') ?? '587', 10),
        user: smtpUser,
        pass: smtpPass,
        from: smtpFrom ?? '<EMAIL>',
        dailyLimit: 500, // Gmail daily limit
        currentUsage: 0,
        type: 'smtp',
      };
      
      console.log(`[EMAIL] Initialized Gmail provider`);
    } else {
      console.log(`[EMAIL] No email provider configured - emails will be logged instead`);
    }
  }

  private async sendEmailWithProvider(
    email: string,
    subject: string,
    htmlContent: string,
    errorContext: string
  ): Promise<void> {
    if (!this.emailProvider) {
      throw new ServiceUnavailableException('No email provider configured');
    }

    try {
      console.log(`[EMAIL DEBUG] Attempting to send ${errorContext} email via ${this.emailProvider.name} to ${email}`);
      
      if (this.emailProvider.type === 'sendgrid') {
        await this.sendEmailWithSendGrid(email, subject, htmlContent);
      } else {
        await this.sendEmailWithSMTP(email, subject, htmlContent);
      }

      // Increment usage counter
      this.emailProvider.currentUsage++;
      
      console.log(`[EMAIL SUCCESS] Email sent successfully via ${this.emailProvider.name} to ${email}`);
      console.log(`[EMAIL USAGE] ${this.emailProvider.name}: ${this.emailProvider.currentUsage}/${this.emailProvider.dailyLimit}`);
    } catch (err) {
      console.error('[EMAIL ERROR] Error sending email', {
        context: errorContext,
        provider: this.emailProvider.name,
        to: email,
        error: err
      });
      
      // If it's a limit exceeded error, mark this provider as at limit
      if (err instanceof Error && err.message.includes('Daily user sending limit exceeded')) {
        this.emailProvider.currentUsage = this.emailProvider.dailyLimit;
        console.log(`[EMAIL LIMIT] ${this.emailProvider.name} daily limit reached`);
        
        // Provide helpful error message with solutions
        throw new ServiceUnavailableException(`${this.emailProvider.name} daily sending limit exceeded (${this.emailProvider.dailyLimit} emails/day). Solutions: 1) Wait until tomorrow, 2) Upgrade your plan, 3) Implement email queuing for retry later`);
      }
      
      throw err;
    }
  }

  private async sendEmailWithSendGrid(email: string, subject: string, htmlContent: string): Promise<void> {
    await this.sendGridService.sendEmail(email, subject, htmlContent, this.emailProvider!.from);
  }

  private async sendEmailWithSMTP(email: string, subject: string, htmlContent: string): Promise<void> {
    // Send email with timeout
    const result = await Promise.race([
      this.mailerService.sendMail({
        to: email,
        subject,
        html: htmlContent,
        from: this.emailProvider!.from,
      }),
      new Promise((_, reject) => setTimeout(() => reject(new Error('timeout')), 5000)),
    ]);

    // Check for sendMail failure before logging success
    if (result === false) {
      throw new ServiceUnavailableException('Failed to send email');
    }
  }

  private async sendEmailLink({
    email,
    token,
    urlPath,
    subject,
    htmlMessage,
    errorContext,
  }: {
    email: string;
    token: string;
    urlPath: string;
    subject: string;
    htmlMessage: string;
    errorContext: string;
  }): Promise<void> {
    if (!email || !/^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/.test(email)) {
      throw new BadRequestException('Invalid email address');
    }
    if (!token) {
      throw new BadRequestException('Token is required');
    }
    
    const appUrl = this.configService.get<string>('APP_URL');
    const url = `${appUrl}${urlPath}?token=${token}`;
    const htmlContent = htmlMessage.replace(/\$\{url\}/g, url);
    
    // Check if email provider is configured
    if (!this.emailProvider) {
      // No provider configured, log the email instead
      console.log(`[MOCK EMAIL] ${errorContext} email would be sent:`);
      console.log(`To: ${email}`);
      console.log(`Subject: ${subject}`);
      console.log(`URL: ${url}`);
      console.log(`HTML: ${htmlContent}`);
      console.log('---');
      return;
    }
    
    // Check if daily limit has been reached
    if (this.emailProvider.currentUsage >= this.emailProvider.dailyLimit) {
      console.error(`[EMAIL LIMIT] Daily limit reached for ${this.emailProvider.name}`);
      throw new ServiceUnavailableException(`Daily email sending limit exceeded. Current usage: ${this.emailProvider.currentUsage}/${this.emailProvider.dailyLimit}`);
    }

    await this.sendEmailWithProvider(email, subject, htmlContent, errorContext);
  }

  async sendVerificationLink(email: string, token: string): Promise<void> {
    return this.sendEmailLink({
      email,
      token,
      urlPath: '/verify-email',
      subject: 'Welcome! Confirm your Email',
      htmlMessage: '<p>Please click the link below to verify your email address:</p><p><a href="${url}">${url}</a></p>',
      errorContext: 'verification',
    });
  }

  async sendPasswordResetLink(email: string, token: string): Promise<void> {
    return this.sendEmailLink({
      email,
      token,
      urlPath: '/reset-password',
      subject: 'Your Password Reset Request',
      htmlMessage: '<p>Please click the link below to reset your password:</p><p><a href="${url}">${url}</a></p>',
      errorContext: 'password reset',
    });
  }

  // Method to get current email provider status
  getEmailProviderStatus() {
    if (!this.emailProvider) {
      return {
        totalProviders: 0,
        providers: [],
        currentProvider: 'None',
        configured: false,
      };
    }

    return {
      totalProviders: 1,
      providers: [{
        name: this.emailProvider.name,
        type: this.emailProvider.type,
        usage: `${this.emailProvider.currentUsage}/${this.emailProvider.dailyLimit}`,
        available: this.emailProvider.currentUsage < this.emailProvider.dailyLimit,
      }],
      currentProvider: this.emailProvider.name,
      configured: true,
    };
  }
} 