import { ApiProperty } from '@nestjs/swagger';
import { UserResponse } from './user-response.dto';

export class AdminUserListResponse {
  @ApiProperty({
    description: 'List of users',
    type: [UserResponse],
  })
  users: UserResponse[];

  @ApiProperty({
    description: 'Total number of users',
    example: 25,
  })
  totalCount: number;

  @ApiProperty({
    description: 'Response message',
    example: 'Users retrieved successfully',
  })
  message: string;

  constructor(users: UserResponse[]) {
    this.users = users;
    this.totalCount = users.length;
    this.message = 'Users retrieved successfully';
  }
}
