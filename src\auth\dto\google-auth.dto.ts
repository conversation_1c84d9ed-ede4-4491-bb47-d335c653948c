import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsOptional, Length } from 'class-validator';

export class GoogleAuthResponseDto {
  @ApiProperty({ description: 'JWT access token' })
  @IsString()
  accessToken!: string;

  @ApiProperty({ description: 'User information' })
  user!: {
    id: string;
    email: string;
    firstName: string;
    lastName: string;
    picture?: string;
    isEmailVerified: boolean;
  };
}

export class GoogleCallbackDto {
  @ApiProperty({ description: 'Authorization code from Google' })
  @IsString()
  @Length(1)
  code!: string;

  @ApiProperty({ description: 'State parameter for CSRF protection', required: false })
  @IsOptional()
  @IsString()
  state?: string;
} 