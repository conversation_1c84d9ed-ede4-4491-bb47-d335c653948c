const axios = require('axios');

const BASE_URL = 'http://localhost:8080';

async function monitorOAuthFlow() {
  console.log('🔍 Google OAuth Flow Monitor\n');
  console.log('📋 Instructions:');
  console.log('1. Open your browser and go to: http://localhost:8080/auth/google');
  console.log('2. Complete the Google sign-in process');
  console.log('3. Watch this monitor for real-time status updates\n');
  
  console.log('⏳ Waiting for OAuth flow to start...\n');
  
  // Monitor the status endpoint every 2 seconds
  const interval = setInterval(async () => {
    try {
      const response = await axios.get(`${BASE_URL}/oauth2/status`);
      const timestamp = new Date().toLocaleTimeString();
      console.log(`[${timestamp}] ✅ Server is running - OAuth endpoints available`);
    } catch (error) {
      const timestamp = new Date().toLocaleTimeString();
      console.log(`[${timestamp}] ❌ Server connection failed: ${error.message}`);
    }
  }, 2000);
  
  // Stop monitoring after 5 minutes
  setTimeout(() => {
    clearInterval(interval);
    console.log('\n⏰ Monitoring stopped. If you haven\'t completed the OAuth flow yet,');
    console.log('   you can restart this monitor and try again.');
  }, 300000);
}

// Run the monitor
monitorOAuthFlow(); 