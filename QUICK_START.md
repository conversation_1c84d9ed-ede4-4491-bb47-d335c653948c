# 🚀 Quick Start Guide

Get the AI Nest Backend up and running in minutes!

## 📋 Prerequisites

- **Node.js** (v18+ recommended)
- **npm** or **yarn**
- **PostgreSQL** (or Docker for easy setup)
- **Git**

## ⚡ Quick Setup

### 1. Clone the Repository
```bash
git clone <your-repository-url>
cd ai-nest-backend
```

### 2. Install Dependencies
```bash
npm install
```

### 3. Set Up Environment (Automated)
```bash
npm run setup
```
This will:
- Create all necessary environment files
- Guide you through configuration
- Generate secure secrets automatically

### 4. Start Database (Docker - Recommended)
```bash
docker-compose up -d
```

### 5. Run Database Migrations
```bash
npm run migration:latest
```

### 6. Start Development Server
```bash
npm run start:dev
```

### 7. Verify Setup
Open your browser and visit:
- **Health Check:** http://localhost:8080/health
- **API Documentation:** http://localhost:8080/api (development only)

## 🎯 What You Get

After setup, you'll have:

✅ **Authentication System**
- JWT-based authentication
- Google OAuth integration
- User registration/login
- Email verification

✅ **Database**
- PostgreSQL with Knex.js
- User management
- Migration system

✅ **Security**
- Environment-based configuration
- Secure session management
- CORS protection
- Input validation

✅ **Development Tools**
- Hot reload
- Comprehensive testing
- API documentation
- Error handling

## 🔧 Manual Setup (Alternative)

If you prefer manual setup:

### 1. Create Environment Files
```bash
cp .env.example .env.development
cp .env.example .env.production
cp .env.example .env.beta
```

### 2. Configure Development Environment
Edit `.env.development` with your settings:

```bash
# Database
DB_HOST=localhost
DB_PORT=5433
DB_USER=postgres
DB_PASSWORD=password
DB_NAME=userauth

# Security (generate strong secrets!)
JWT_SECRET=your-strong-jwt-secret
SESSION_SECRET=your-strong-session-secret

# Email (for development)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password

# Google OAuth (optional for basic testing)
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret
```

## 🧪 Testing

Run the test suite to verify everything works:

```bash
# Unit tests
npm test

# Test coverage
npm run test:coverage

# End-to-end tests
npm run test:e2e
```

## 📚 Next Steps

### For Development
1. **Read the Documentation**
   - [Environment Setup Guide](./ENVIRONMENT_SETUP.md)
   - [Testing Guide](./TESTING.md)
   - [Main README](./README.md)

2. **Set Up Google OAuth**
   - Create Google Cloud project
   - Configure OAuth consent screen
   - Get client credentials

3. **Explore the API**
   - Visit http://localhost:8080/api for Swagger docs
   - Test authentication endpoints
   - Try the health check endpoint

### For Production
1. **Configure Production Environment**
   ```bash
   # Edit .env.production with production values
   nano .env.production
   ```

2. **Set Up Production Database**
   - Use managed PostgreSQL service
   - Configure connection pooling
   - Set up backups

3. **Deploy**
   - Build the application: `npm run build`
   - Use Docker or your preferred deployment method
   - Set up monitoring and logging

## 🆘 Troubleshooting

### Common Issues

**"Cannot connect to database"**
```bash
# Check if PostgreSQL is running
docker-compose ps

# Restart database
docker-compose restart postgres
```

**"Environment variable not found"**
```bash
# Re-run setup
npm run setup

# Or manually check your .env file
cat .env.development
```

**"Port already in use"**
```bash
# Change port in .env file
PORT=8081

# Or kill process using port 8080
lsof -ti:8080 | xargs kill -9
```

### Getting Help

1. Check the logs: `npm run start:dev`
2. Review environment configuration
3. Run tests to identify issues
4. Check the [Environment Setup Guide](./ENVIRONMENT_SETUP.md)

## 🎉 You're Ready!

Your AI Nest Backend is now running! Start building amazing features with:

- **Secure Authentication** 🔐
- **Database Integration** 🗄️
- **Email Services** 📧
- **OAuth Integration** 🔗
- **Comprehensive Testing** ✅

Happy coding! 🚀

---

**Need more details?** Check out the [full documentation](./README.md) and [environment setup guide](./ENVIRONMENT_SETUP.md).
