// WARNING: All infrastructure dependencies (DB, email, external services) must be mocked using jest.fn(). No real calls allowed in unit tests.
import { EmailService } from '../../src/email/email.service';
import { MailerService } from '@nestjs-modules/mailer';
import { ConfigService } from '@nestjs/config';
import { SendGridService } from '../../src/email/sendgrid.service';

describe('EmailService (Integration)', () => {
  let emailService: EmailService;
  let mailerService: MailerService;
  let configService: ConfigService;
  let sendGridService: SendGridService;

  beforeEach(() => {
    // Set test environment
    process.env.NODE_ENV = 'test';
    
    mailerService = {
      sendMail: jest.fn().mockResolvedValue(true),
    } as any;
    configService = {
      get: jest.fn((key: string) => {
        if (key === 'email.host') return undefined; // Simulate no SMTP config (logs only)
        if (key === 'APP_URL') return 'http://localhost:3000';
        return undefined;
      }),
    } as any;
    sendGridService = {
      isAvailable: jest.fn().mockReturnValue(false),
      sendEmail: jest.fn(),
    } as any;
    emailService = new EmailService(mailerService, configService, sendGridService);
  });

  it('should log verification email when no SMTP is configured', async () => {
    const consoleSpy = jest.spyOn(console, 'log').mockImplementation();
    await emailService.sendVerificationLink('<EMAIL>', 'token123');
    expect(consoleSpy).toHaveBeenCalledWith(expect.stringContaining('[MOCK EMAIL]'));
    consoleSpy.mockRestore();
  });

  it('should log password reset email when no SMTP is configured', async () => {
    const consoleSpy = jest.spyOn(console, 'log').mockImplementation();
    await emailService.sendPasswordResetLink('<EMAIL>', 'token123');
    expect(consoleSpy).toHaveBeenCalledWith(expect.stringContaining('[MOCK EMAIL]'));
    consoleSpy.mockRestore();
  });

  describe('with configured SMTP', () => {
    beforeEach(() => {
      // Set environment to 'smtp' for configured SMTP tests
      process.env.NODE_ENV = 'smtp';
      
      configService = {
        get: jest.fn((key: string) => {
          switch (key) {
            case 'email.host': return 'smtp.gmail.com';
            case 'email.port': return '587';
            case 'email.user': return '<EMAIL>';
            case 'email.pass': return 'testpass';
            case 'email.from': return '<EMAIL>';
            case 'APP_URL': return 'http://localhost:3000';
            default: return undefined;
          }
        }),
      } as any;
      sendGridService = {
        isAvailable: jest.fn().mockReturnValue(false),
        sendEmail: jest.fn(),
      } as any;
      emailService = new EmailService(mailerService, configService, sendGridService);
    });

    it('should throw for daily limit exceeded', async () => {
      // Manually set usage to limit
      (emailService as any).emailProvider.currentUsage = 500;
      await expect(emailService.sendVerificationLink('<EMAIL>', 'token123')).rejects.toThrow('Daily email sending limit exceeded');
    });
  });

  describe('with SendGrid configuration', () => {
    beforeEach(() => {
      // Set test environment
      process.env.NODE_ENV = 'test';

      mailerService = {
        sendMail: jest.fn().mockResolvedValue(true),
      } as any;
      configService = {
        get: jest.fn((key: string) => {
          if (key === 'email.provider') return 'sendgrid';
          if (key === 'email.sendgridApiKey') return 'test-sendgrid-api-key';
          if (key === 'email.sendgridFrom') return '<EMAIL>';
          if (key === 'APP_URL') return 'http://localhost:3000';
          return undefined;
        }),
      } as any;
      sendGridService = {
        isAvailable: jest.fn().mockReturnValue(true),
        sendEmail: jest.fn().mockResolvedValue(undefined),
      } as any;
      emailService = new EmailService(mailerService, configService, sendGridService);
    });

    it('should use SendGrid when configured and available', async () => {
      // Set environment to use SendGrid
      process.env.NODE_ENV = 'sendgrid';

      configService = {
        get: jest.fn((key: string) => {
          switch (key) {
            case 'email.provider': return 'sendgrid';
            case 'email.sendgridApiKey': return 'test-sendgrid-api-key';
            case 'email.sendgridFrom': return '<EMAIL>';
            case 'APP_URL': return 'http://localhost:3000';
            default: return undefined;
          }
        }),
      } as any;
      sendGridService = {
        isAvailable: jest.fn().mockReturnValue(true),
        sendEmail: jest.fn().mockResolvedValue(undefined),
      } as any;

      emailService = new EmailService(mailerService, configService, sendGridService);

      await emailService.sendVerificationLink('<EMAIL>', 'token123');

      expect(sendGridService.sendEmail).toHaveBeenCalledWith(
        '<EMAIL>',
        'Welcome! Confirm your Email',
        expect.stringContaining('http://localhost:3000/verify-email?token=token123'),
        '<EMAIL>'
      );
    });

    it('should handle SendGrid errors gracefully', async () => {
      // Set environment to use SendGrid
      process.env.NODE_ENV = 'sendgrid';

      configService = {
        get: jest.fn((key: string) => {
          switch (key) {
            case 'email.provider': return 'sendgrid';
            case 'email.sendgridApiKey': return 'test-sendgrid-api-key';
            case 'email.sendgridFrom': return '<EMAIL>';
            case 'APP_URL': return 'http://localhost:3000';
            default: return undefined;
          }
        }),
      } as any;
      sendGridService = {
        isAvailable: jest.fn().mockReturnValue(true),
        sendEmail: jest.fn().mockRejectedValue(new Error('SendGrid API error')),
      } as any;

      emailService = new EmailService(mailerService, configService, sendGridService);

      await expect(emailService.sendVerificationLink('<EMAIL>', 'token123'))
        .rejects.toThrow('SendGrid API error');
    });

    it('should fall back when SendGrid is not available', async () => {
      // Set environment to use SendGrid but make it unavailable
      process.env.NODE_ENV = 'sendgrid';

      configService = {
        get: jest.fn((key: string) => {
          switch (key) {
            case 'email.provider': return 'sendgrid';
            case 'email.sendgridApiKey': return undefined; // No API key
            case 'email.sendgridFrom': return '<EMAIL>';
            case 'APP_URL': return 'http://localhost:3000';
            default: return undefined;
          }
        }),
      } as any;
      sendGridService = {
        isAvailable: jest.fn().mockReturnValue(false),
        sendEmail: jest.fn(),
      } as any;

      emailService = new EmailService(mailerService, configService, sendGridService);

      const consoleSpy = jest.spyOn(console, 'log').mockImplementation();
      await emailService.sendVerificationLink('<EMAIL>', 'token123');

      // Should log mock email instead of using SendGrid
      expect(consoleSpy).toHaveBeenCalledWith(expect.stringContaining('[MOCK EMAIL]'));
      expect(sendGridService.sendEmail).not.toHaveBeenCalled();

      consoleSpy.mockRestore();
    });
  });
});