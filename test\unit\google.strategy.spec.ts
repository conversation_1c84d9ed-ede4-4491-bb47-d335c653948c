// WARNING: All infrastructure dependencies (DB, email, external services) must be mocked using jest.fn(). No real calls allowed in unit tests.
import { ConfigService } from '@nestjs/config';
import { GoogleStrategy } from '../../src/auth/google.strategy';

describe('GoogleStrategy', () => {
  let configService: ConfigService;

  beforeEach(() => {
    configService = {
      get: jest.fn((key: string) => {
        if (key === 'googleOAuth') {
          return {
            clientId: 'clientId',
            clientSecret: 'clientSecret',
            redirectUri: 'http://localhost:8080/oauth2/callback/google',
            scope: 'email,profile',
          };
        }
        return undefined;
      }),
    } as any;
  });

  it('should construct with correct config and parse scope/callbackURL', () => {
    const strategy = new GoogleStrategy(configService);
    expect(strategy).toBeInstanceOf(GoogleStrategy);
  });

  it('should replace {baseUrl} in callbackURL with APP_URL or default', () => {
    // With APP_URL
    configService.get = jest.fn((key: string) => {
      if (key === 'googleOAuth') {
        return {
          clientId: 'clientId',
          clientSecret: 'clientSecret',
          redirectUri: '{baseUrl}/oauth2/callback/google',
          scope: 'email,profile',
        };
      }
      if (key === 'APP_URL') return 'http://myapp.com';
      return undefined;
    }) as any;
    const strategy = new GoogleStrategy(configService);
    expect(strategy).toBeInstanceOf(GoogleStrategy);
    // Without APP_URL
    configService.get = jest.fn((key: string) => {
      if (key === 'googleOAuth') {
        return {
          clientId: 'clientId',
          clientSecret: 'clientSecret',
          redirectUri: '{baseUrl}/oauth2/callback/google',
          scope: 'email,profile',
        };
      }
      if (key === 'APP_URL') return undefined;
      return undefined;
    }) as any;
    const strategy2 = new GoogleStrategy(configService);
    expect(strategy2).toBeInstanceOf(GoogleStrategy);
  });

  it('should parse scope as array', () => {
    configService.get = jest.fn((key: string) => {
      if (key === 'googleOAuth') {
        return {
          clientId: 'clientId',
          clientSecret: 'clientSecret',
          redirectUri: '{baseUrl}/oauth2/callback/google',
          scope: 'email, profile, openid',
        };
      }
      if (key === 'APP_URL') return 'http://myapp.com';
      return undefined;
    }) as any;
    const strategy = new GoogleStrategy(configService);
    expect(strategy).toBeInstanceOf(GoogleStrategy);
  });

  it('should validate and call done with user object', async () => {
    configService.get = jest.fn((key: string) => {
      if (key === 'googleOAuth') {
        return {
          clientId: 'clientId',
          clientSecret: 'clientSecret',
          redirectUri: 'http://localhost:8080/oauth2/callback/google',
          scope: 'email,profile',
        };
      }
      return undefined;
    }) as any;
    const strategy = new GoogleStrategy(configService);
    const done = jest.fn();
    const profile = {
      id: 'googleid',
      name: { givenName: 'First', familyName: 'Last' },
      emails: [{ value: '<EMAIL>' }],
      photos: [{ value: 'http://photo.url' }],
    };
    await strategy.validate('access', 'refresh', profile, done);
    expect(done).toHaveBeenCalledWith(null, {
      email: '<EMAIL>',
      firstName: 'First',
      lastName: 'Last',
      picture: 'http://photo.url',
      accessToken: 'access',
      googleId: 'googleid',
    });
  });
}); 