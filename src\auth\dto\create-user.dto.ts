import { IsEmail, IsString, Length } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class CreateUserDto {
  @IsEmail()
  email!: string;

  @IsString()
  @Length(8, 255)
  password!: string;

  @ApiProperty({ 
    description: 'User IP address',
    example: '*********'
  })
  @IsString()
  @Length(1)
  ipAddress!: string;

  @ApiProperty({ 
    description: 'Device details',
    example: 'Chrome 120.0.0.0 on Windows 10'
  })
  @IsString()
  @Length(1)
  deviceDetails!: string;
} 