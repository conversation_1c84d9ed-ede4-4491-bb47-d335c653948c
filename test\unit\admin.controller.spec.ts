import { Test, TestingModule } from '@nestjs/testing';
import { AdminController } from '../../src/auth/admin.controller';
import { AuthService } from '../../src/auth/auth.service';
import { AdminGuard } from '../../src/auth/admin.guard';
import { AdminUserListResponse, UserResponse } from '../../src/auth/dto';
import { LoggerService } from '../../src/common/logger/logger.service';

describe('AdminController', () => {
  let controller: AdminController;
  let authService: jest.Mocked<AuthService>;
  let loggerService: jest.Mocked<LoggerService>;

  beforeEach(async () => {
    const mockAuthService = {
      getAllUsers: jest.fn(),
    };

    const mockLoggerService = {
      setContext: jest.fn(),
      log: jest.fn(),
      error: jest.fn(),
      warn: jest.fn(),
      debug: jest.fn(),
    };

    const mockKnexConnection = {
      where: jest.fn().mockReturnThis(),
      first: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      controllers: [AdminController],
      providers: [
        {
          provide: AuthService,
          useValue: mockAuthService,
        },
        {
          provide: LoggerService,
          useValue: mockLoggerService,
        },
        {
          provide: AdminGuard,
          useValue: {
            canActivate: jest.fn().mockReturnValue(true),
          },
        },
        {
          provide: 'KNEX_CONNECTION',
          useValue: mockKnexConnection,
        },
      ],
    })
    .overrideGuard(AdminGuard)
    .useValue({
      canActivate: jest.fn().mockReturnValue(true),
    })
    .compile();

    controller = module.get<AdminController>(AdminController);
    authService = module.get(AuthService);
    loggerService = module.get(LoggerService);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('getAllUsers', () => {
    it('should return all users successfully', async () => {
      const mockUsers: UserResponse[] = [
        {
          id: '123e4567-e89b-12d3-a456-************',
          email: '<EMAIL>',
          name: 'User One',
          mobileNumber: '**********',
          role: 'USER',
          isActive: true,
          isEmailVerified: true,
          isAccountLocked: false,
          socialLoginProvider: undefined,
          createdOn: new Date('2024-01-01'),
          updatedOn: new Date('2024-01-01'),
          lastLoginAt: new Date('2024-01-01'),
        },
        {
          id: '456e7890-e89b-12d3-a456-************',
          email: '<EMAIL>',
          name: 'Admin User',
          mobileNumber: '**********',
          role: 'ADMIN',
          isActive: true,
          isEmailVerified: true,
          isAccountLocked: false,
          socialLoginProvider: undefined,
          createdOn: new Date('2024-01-01'),
          updatedOn: new Date('2024-01-01'),
          lastLoginAt: new Date('2024-01-01'),
        },
      ];

      const expectedResponse = new AdminUserListResponse(mockUsers);
      authService.getAllUsers.mockResolvedValue(expectedResponse);

      const result = await controller.getAllUsers();

      expect(authService.getAllUsers).toHaveBeenCalledTimes(1);
      expect(loggerService.log).toHaveBeenCalledWith(
        'Admin requesting all users',
        'AdminController'
      );
      expect(result).toEqual(expectedResponse);
      expect(result.users).toHaveLength(2);
      expect(result.totalCount).toBe(2);
      expect(result.message).toBe('Users retrieved successfully');
    });

    it('should return empty list when no users exist', async () => {
      const expectedResponse = new AdminUserListResponse([]);
      authService.getAllUsers.mockResolvedValue(expectedResponse);

      const result = await controller.getAllUsers();

      expect(authService.getAllUsers).toHaveBeenCalledTimes(1);
      expect(result.users).toHaveLength(0);
      expect(result.totalCount).toBe(0);
      expect(result.message).toBe('Users retrieved successfully');
    });

    it('should handle service errors', async () => {
      const error = new Error('Database connection failed');
      authService.getAllUsers.mockRejectedValue(error);

      await expect(controller.getAllUsers()).rejects.toThrow('Database connection failed');
      expect(authService.getAllUsers).toHaveBeenCalledTimes(1);
    });
  });
});
